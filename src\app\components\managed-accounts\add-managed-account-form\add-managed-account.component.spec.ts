import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { AddManagedAccountComponent } from './add-managed-account.component';
import { ManagedAccountService } from '../managed-account.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
import { ManagedAccount } from '../managed-account.model';

describe('AddManagedAccountComponent', () => {
  let component: AddManagedAccountComponent;
  let fixture: ComponentFixture<AddManagedAccountComponent>;
  let mockManagedAccountService: jasmine.SpyObj<ManagedAccountService>;
  let mockBreadcrumbService: jasmine.SpyObj<BreadcrumbService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockToastrService: jasmine.SpyObj<ToastrService>;
  let mockActivatedRoute: any;
  let formBuilder: FormBuilder;

  const mockManagedAccount: ManagedAccount = {
    managedAccountName: 'Test Account',
    domicile: 'Test Domicile',
    commencementDate: new Date('2023-01-01'),
    investmentPeriodEndDate: '2025-01-01',
    maturityDate: '2028-01-01',
    commitmentOutstanding: '1000000',
    baseCurrency: 'USD',
    investmentManager: 'Test Manager',
    administrator: 'Test Administrator',
    custodian: 'Test Custodian',
    legalCounsel: 'Test Legal',
    lei: 'TEST123456789',
    investmentSummary: 'Test summary'
  };

  beforeEach(async () => {
    const managedAccountServiceSpy = jasmine.createSpyObj('ManagedAccountService', [
      'saveManagedAccount', 'getManagedAccountById', 'emitGoToStep', 'updateManagedAccount'
    ], { goToStep$: of(1) });
    const breadcrumbServiceSpy = jasmine.createSpyObj('BreadcrumbService', ['setBreadcrumbs']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error']);

    // Mock ActivatedRoute with both paramMap and queryParams
    mockActivatedRoute = {
      paramMap: of({ get: (key: string) => key === 'id' ? '0' : null }),
      queryParams: of({})
    };

    await TestBed.configureTestingModule({
      declarations: [AddManagedAccountComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: ManagedAccountService, useValue: managedAccountServiceSpy },
        { provide: BreadcrumbService, useValue: breadcrumbServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        DatePipe,
        FormBuilder
      ]
    }).compileComponents();

    mockManagedAccountService = TestBed.inject(ManagedAccountService) as jasmine.SpyObj<ManagedAccountService>;
    mockBreadcrumbService = TestBed.inject(BreadcrumbService) as jasmine.SpyObj<BreadcrumbService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockToastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    formBuilder = TestBed.inject(FormBuilder);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddManagedAccountComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', fakeAsync(() => {
    // Trigger ngOnInit and wait for subscriptions
    component.ngOnInit();
    tick();
    
    expect(component.step).toBe(1);
    expect(component.isLoading).toBeFalse();
    expect(component.submitted).toBeFalse();
    expect(component.showPopup).toBeFalse();
    expect(component.managedAccountId).toBe('0'); // Based on mockActivatedRoute
  }));

  it('should call getManagedAccountById when id is not 0', fakeAsync(() => {
    // Update mock to return a non-zero ID
    mockActivatedRoute.paramMap = of({ get: (key: string) => key === 'id' ? '1' : null });
    
    spyOn(component, 'updateBreadcrumbs');
    spyOn(component, 'getManagedAccountById');
    
    // Recreate component with new mock
    fixture = TestBed.createComponent(AddManagedAccountComponent);
    component = fixture.componentInstance;
    
    component.ngOnInit();
    tick();

    expect(component.getManagedAccountById).toHaveBeenCalledWith('1');
  }));

  it('should handle query params for step navigation', fakeAsync(() => {
    // Mock query params with step
    mockActivatedRoute.queryParams = of({ step: '2' });
    
    // Recreate component with new mock
    fixture = TestBed.createComponent(AddManagedAccountComponent);
    component = fixture.componentInstance;
    
    component.ngOnInit();
    tick();

    expect(component.step).toBe(2);
    expect(component.isEdited).toBeTrue();
    expect(component.isFromReviewPage).toBeTrue();
  }));

  describe('updateBreadcrumbs', () => {
    it('should set breadcrumbs for add mode', () => {
      component.managedAccountId = '';
      component.updateBreadcrumbs();

      expect(mockBreadcrumbService.setBreadcrumbs).toHaveBeenCalledWith([
        { label: 'Managed Accounts', url: '/managed-accounts' },
        { label: 'Add Managed Account' }
      ]);
    });

    it('should set breadcrumbs for update mode', () => {
      component.managedAccountId = '1';
      component.updateBreadcrumbs();

      expect(mockBreadcrumbService.setBreadcrumbs).toHaveBeenCalledWith([
        { label: 'Managed Accounts', url: '/managed-accounts' },
        { label: 'Update Managed Account' }
      ]);
    });
  });

  describe('goToStep', () => {
    it('should update step and set edited flag', () => {
      component.isSummarySaved = true;
      component.step = 1;
      
      component.goToStep(2);

      expect(component.step).toBe(2);
      expect(component.isEdited).toBeTrue();
    });
  });

  describe('onCancel', () => {
    it('should navigate to managed accounts', () => {
      component.onCancel();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/managed-accounts']);
    });
  });

  describe('onCloseDialog', () => {
    it('should close popup and navigate to managed accounts', () => {
      component.showPopup = true;
      
      component.onCloseDialog();

      expect(component.showPopup).toBeFalse();
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/managed-accounts']);
    });
  });

  describe('updateCharCount', () => {
    it('should update character count and set summary edited flag', () => {
      component.managedAccountForm = formBuilder.group({
        SummaryCount: ['Test summary']
      });
      
      component.updateCharCount();

      expect(component.charCount).toBe(12);
      expect(component.isSummaryEdited).toBeTrue();
    });
  });

  describe('setCurrentDate', () => {
    it('should set current date when control is empty', () => {
      component.managedAccountForm = formBuilder.group({
        commencementDate: ['']
      });
      
      component.setCurrentDate('commencementDate');

      expect(component.managedAccountForm.get('commencementDate')?.value).toBeDefined();
    });

    it('should not set date when control already has value', () => {
      const existingDate = new Date('2023-01-01');
      component.managedAccountForm = formBuilder.group({
        commencementDate: [existingDate]
      });
      
      component.setCurrentDate('commencementDate');

      expect(component.managedAccountForm.get('commencementDate')?.value).toBe(existingDate);
    });
  });

  describe('onSubmit', () => {
    beforeEach(() => {
      component.managedAccountForm = formBuilder.group({
        managedAccountName: ['Test Account'],
        domicile: ['Test Domicile'],
        commencementDate: [new Date('2023-01-01')],
        investmentPeriodEndDate: ['2025-01-01'],
        maturityDate: ['2028-01-01'],
        commitmentOutstanding: ['1000000'],
        baseCurrency: ['USD'],
        investmentManager: ['Test Manager'],
        administrator: ['Test Administrator'],
        custodian: ['Test Custodian'],
        legalCounsel: ['Test Legal'],
        lei: ['TEST123456789'],
        SummaryCount: ['Test summary']
      });
    });

    it('should handle form submission with empty fields', () => {
      component.managedAccountForm.patchValue({ managedAccountName: '' });
      
      component.onSubmit();

      expect(component.submitted).toBeTrue();
      expect(mockToastrService.error).toHaveBeenCalled();
    });

    it('should handle step 1 submission for add mode', () => {
      component.step = 1;
      component.isFromReviewPage = false;
      component.managedAccountId = ''; // Ensure we're in add mode
      
      // Set up a valid form
      component.managedAccountForm = formBuilder.group({
        managedAccountName: ['Test Account'],
        domicile: ['Test Domicile'],
        commencementDate: [new Date('2023-01-01')],
        investmentPeriodEndDate: ['2025-01-01'],
        maturityDate: ['2028-01-01'],
        commitmentOutstanding: ['1000000'],
        baseCurrency: ['USD'],
        investmentManager: ['Test Manager'],
        administrator: ['Test Administrator'],
        custodian: ['Test Custodian'],
        legalCounsel: ['Test Legal'],
        lei: ['TEST123456789'],
        SummaryCount: ['Test summary']
      });
      
      component.onSubmit();

      expect(component.isAccountFactsSaved).toBeTrue();
      expect(component.step).toBe(2);
      expect(component.isEdited).toBeFalse();
    });

    it('should handle step 1 submission for review page', () => {
      component.step = 1;
      component.isFromReviewPage = true;
      component.managedAccountId = ''; // Ensure we're in add mode
      
      // Set up a valid form
      component.managedAccountForm = formBuilder.group({
        managedAccountName: ['Test Account'],
        domicile: ['Test Domicile'],
        commencementDate: [new Date('2023-01-01')],
        investmentPeriodEndDate: ['2025-01-01'],
        maturityDate: ['2028-01-01'],
        commitmentOutstanding: ['1000000'],
        baseCurrency: ['USD'],
        investmentManager: ['Test Manager'],
        administrator: ['Test Administrator'],
        custodian: ['Test Custodian'],
        legalCounsel: ['Test Legal'],
        lei: ['TEST123456789'],
        SummaryCount: ['Test summary']
      });
      
      component.onSubmit();

      expect(component.isAccountFactsSaved).toBeTrue();
      expect(component.step).toBe(3);
      expect(component.isFromReviewPage).toBeFalse();
    });

    it('should handle step 2 submission', () => {
      component.step = 2;
      component.managedAccountId = ''; // Ensure we're in add mode
      
      // Set up a valid form
      component.managedAccountForm = formBuilder.group({
        managedAccountName: ['Test Account'],
        domicile: ['Test Domicile'],
        commencementDate: [new Date('2023-01-01')],
        investmentPeriodEndDate: ['2025-01-01'],
        maturityDate: ['2028-01-01'],
        commitmentOutstanding: ['1000000'],
        baseCurrency: ['USD'],
        investmentManager: ['Test Manager'],
        administrator: ['Test Administrator'],
        custodian: ['Test Custodian'],
        legalCounsel: ['Test Legal'],
        lei: ['TEST123456789'],
        SummaryCount: ['Test summary']
      });
      
      component.onSubmit();

      expect(component.isSummarySaved).toBeTrue();
      expect(component.step).toBe(3);
      expect(component.isEdited).toBeFalse();
    });

    it('should call saveManagedAccount for update mode', () => {
      component.managedAccountId = '1';
      spyOn(component, 'saveManagedAccount');
      
      // Set up a valid form
      component.managedAccountForm = formBuilder.group({
        managedAccountName: ['Test Account'],
        domicile: ['Test Domicile'],
        commencementDate: [new Date('2023-01-01')],
        investmentPeriodEndDate: ['2025-01-01'],
        maturityDate: ['2028-01-01'],
        commitmentOutstanding: ['1000000'],
        baseCurrency: ['USD'],
        investmentManager: ['Test Manager'],
        administrator: ['Test Administrator'],
        custodian: ['Test Custodian'],
        legalCounsel: ['Test Legal'],
        lei: ['TEST123456789'],
        SummaryCount: ['Test summary']
      });
      
      component.onSubmit();

      expect(component.saveManagedAccount).toHaveBeenCalled();
    });
  });

  describe('getManagedAccountById', () => {
    it('should load managed account data successfully', fakeAsync(() => {
      const mockResponse = { ...mockManagedAccount };
      mockManagedAccountService.getManagedAccountById.and.returnValue(of(mockResponse));
      
      // Initialize form first
      component.managedAccountForm = formBuilder.group({
        managedAccountName: [''],
        domicile: [''],
        commencementDate: [''],
        investmentPeriodEndDate: [''],
        maturityDate: [''],
        commitmentOutstanding: [''],
        baseCurrency: [''],
        investmentManager: [''],
        administrator: [''],
        custodian: [''],
        legalCounsel: [''],
        lei: [''],
        SummaryCount: ['']
      });
      
      component.getManagedAccountById('1');
      tick();

      expect(component.isLoading).toBeFalse();
    }));

    it('should handle error when loading managed account', fakeAsync(() => {
      const mockError = { error: { error: 'API Error' } };
      mockManagedAccountService.getManagedAccountById.and.returnValue(throwError(() => mockError));
      
      // Initialize the component first
      component.ngOnInit();
      tick();
      component.getManagedAccountById('1');
      tick();

      expect(component.isLoading).toBeFalse();
      expect(mockToastrService.error).toHaveBeenCalledWith('API Error', '', { positionClass: 'toast-center-center' });
    }));

    it('should handle no content response', fakeAsync(() => {
      const mockResponse = { id: null, managedAccountName: '', domicile: '', commencementDate: new Date(), investmentPeriodEndDate: '', maturityDate: '', commitmentOutstanding: '', baseCurrency: '', investmentManager: '', administrator: '', custodian: '', legalCounsel: '', lei: '', investmentSummary: '' };
      mockManagedAccountService.getManagedAccountById.and.returnValue(of(mockResponse));
      
      // Initialize the component first
      component.ngOnInit();
      tick();
      component.getManagedAccountById('1');
      tick();

      expect(component.isLoading).toBeFalse();
    }));
  });

  describe('saveManagedAccount', () => {
    it('should save managed account successfully', fakeAsync(() => {
      const mockResponse = { id: 1, message: 'Success' };
      mockManagedAccountService.saveManagedAccount.and.returnValue(of(mockResponse));
      mockManagedAccountService.updateManagedAccount.and.returnValue(of(mockResponse));
      
      // Set up the savedData that the method expects
      component.savedData = mockManagedAccount;
      component.saveManagedAccount();
      tick();

      expect(component.isLoading).toBeFalse();
      expect(mockToastrService.success).toHaveBeenCalled();
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/managed-accounts']);
    }));

    it('should handle error when saving managed account', fakeAsync(() => {
      const mockError = { error: { error: 'Save failed' } };
      mockManagedAccountService.saveManagedAccount.and.returnValue(throwError(() => mockError));
      mockManagedAccountService.updateManagedAccount.and.returnValue(throwError(() => mockError));
      
      // Set up the savedData that the method expects
      component.savedData = mockManagedAccount;
      component.saveManagedAccount();
      tick();

      expect(component.isLoading).toBeFalse();
      expect(mockToastrService.error).toHaveBeenCalled();
    }));
  });

  describe('mapFormToModel', () => {
    it('should map form values to ManagedAccount model', () => {
      component.managedAccountForm = formBuilder.group({
        managedAccountName: ['Test Account'],
        domicile: ['Test Domicile'],
        commencementDate: [new Date('2023-01-01')],
        investmentPeriodEndDate: ['2025-01-01'],
        maturityDate: ['2028-01-01'],
        commitmentOutstanding: ['1000000'],
        baseCurrency: ['USD'],
        investmentManager: ['Test Manager'],
        administrator: ['Test Administrator'],
        custodian: ['Test Custodian'],
        legalCounsel: ['Test Legal'],
        lei: ['TEST123456789'],
        SummaryCount: ['Test summary']
      });

      const result = component['mapFormToModel']();

      expect(result.managedAccountName).toBe('Test Account');
      expect(result.investmentSummary).toBe('Test summary');
    });
  });

  describe('onResetInvestmentSummary', () => {
    it('should reset summary field and flags', () => {
      component.managedAccountForm = formBuilder.group({
        SummaryCount: ['Test summary']
      });
      component.isEdited = true;
      component.submitted = true;
      
      component.onResetInvestmentSummary();

      expect(component.managedAccountForm.get('SummaryCount')?.value).toBe('');
      expect(component.isEdited).toBeFalse();
      expect(component.submitted).toBeFalse();
    });
  });

  describe('onResetAccountFacts', () => {
    it('should reset all fields except SummaryCount', () => {
      component.managedAccountForm = formBuilder.group({
        managedAccountName: ['Test'],
        SummaryCount: ['Keep this']
      });
      component.isEdited = true;
      component.submitted = true;
      
      component.onResetAccountFacts();

      expect(component.managedAccountForm.get('managedAccountName')?.value).toBeNull();
      expect(component.managedAccountForm.get('SummaryCount')?.value).toBe('Keep this');
      expect(component.isEdited).toBeFalse();
      expect(component.submitted).toBeFalse();
    });
  });

  describe('onReset', () => {
    it('should call onResetAccountFacts for step 1', () => {
      component.step = 1;
      spyOn(component, 'onResetAccountFacts');
      
      component.onReset();

      expect(component.onResetAccountFacts).toHaveBeenCalled();
    });

    it('should call onResetInvestmentSummary for step 2', () => {
      component.step = 2;
      spyOn(component, 'onResetInvestmentSummary');
      
      component.onReset();

      expect(component.onResetInvestmentSummary).toHaveBeenCalled();
    });
  });
});
