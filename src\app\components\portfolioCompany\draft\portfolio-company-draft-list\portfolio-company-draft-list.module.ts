import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { PortfolioCompanyDraftComponent } from './portfolio-company-draft-list.component';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ScrollingModule,
    SharedDirectiveModule,
    PrimeNgModule
  ],
  declarations: [PortfolioCompanyDraftComponent],
  exports: [PortfolioCompanyDraftComponent]
})
export class PortfolioCompanyDraftModule { }