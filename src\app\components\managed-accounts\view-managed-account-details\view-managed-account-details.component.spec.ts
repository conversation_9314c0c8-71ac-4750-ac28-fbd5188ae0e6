import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { ViewManagedAccountDetailsComponent } from './view-managed-account-details.component';
import { ToastrService } from 'ngx-toastr';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { PanelbarItemService } from '../../clo/shared/panelbar-item/panelbar-item.service';
import { CommonSubFeaturePermissionService } from '../../../services/subPermission.service';
import { BreadcrumbService } from '../../../services/breadcrumb-service.service';
import { DomSanitizer } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { ManagedAccountService } from '../managed-account.service';

describe('ViewManagedAccountDetailsComponent', () => {
  let component: ViewManagedAccountDetailsComponent;
  let fixture: ComponentFixture<ViewManagedAccountDetailsComponent>;
  let mockManagedAccountService: jasmine.SpyObj<ManagedAccountService>;
  let router: Router;
  let pagePanelService: jasmine.SpyObj<PanelbarItemService>;

  beforeEach(async () => {
    mockManagedAccountService = jasmine.createSpyObj('ManagedAccountService', ['getManagedAccountById']);
    mockManagedAccountService.getManagedAccountById.and.returnValue(of({ 
      managedAccountId: '1', 
      managedAccountName: 'Test Managed Account',
      domicile: 'US',
      commencementDate: new Date('2023-01-01'),
      investmentPeriodEndDate: '2025-12-31',
      maturityDate: '2030-12-31',
      commitmentOutstanding: '1000000',
      baseCurrency: 'USD',
      investmentManager: 'Test Manager',
      administrator: 'Test Admin',
      custodian: 'Test Custodian',
      legalCounsel: 'Test Counsel',
      lei: '1234567890ABCDEF',
      investmentSummary: 'Test investment summary'
    }));

    pagePanelService = jasmine.createSpyObj('PanelbarItemService', ['checkTablePermissions', 'updateTableVisibility']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [ViewManagedAccountDetailsComponent],
      imports: [RouterTestingModule, HttpClientTestingModule, FormsModule],
      providers: [
        { provide: 'BASE_URL', useValue: 'http://localhost:4200' },
        { provide: ManagedAccountService, useValue: mockManagedAccountService },
        { provide: ToastrService, useValue: { success: jasmine.createSpy(), error: jasmine.createSpy(), warning: jasmine.createSpy() } },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({ get: (key: string) => '1' })
          }
        },
        { provide: PanelbarItemService, useValue: pagePanelService },
        { provide: Router, useValue: routerSpy },
        { provide: CommonSubFeaturePermissionService, useValue: jasmine.createSpyObj('CommonSubFeaturePermissionService', ['getCommonSubFeatureAccessPermissions']) },
        { provide: BreadcrumbService, useValue: jasmine.createSpyObj('BreadcrumbService', ['setBreadcrumbs']) },
        { provide: DomSanitizer, useValue: jasmine.createSpyObj('DomSanitizer', ['sanitize', 'bypassSecurityTrustHtml']) }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ViewManagedAccountDetailsComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Prevent ngOnInit from running automatically to avoid subscription issues
    spyOn(component, 'ngOnInit').and.callFake(() => {});
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set selectedTab to the provided tab', () => {
    const tab = 1;
    component.highlightTab(tab);
    expect(component.selectedTab).toBe(tab);
  });

  it('should call checkTablePermissions with correct parameters', () => {
    const tableId = 1;
    const permissionType = 'CAN_VIEW';
    component.permissions = [{ subFeatureId: 1, CAN_VIEW: true }];

    pagePanelService.checkTablePermissions.and.returnValue(true);

    const result = component.checkTablePermissions(tableId, permissionType);

    expect(pagePanelService.checkTablePermissions).toHaveBeenCalledWith(tableId, component.permissions, permissionType);
    expect(result).toBe(true);
  });

  it('should call checkTablePermissions with default permissionType when not provided', () => {
    const tableId = 1;
    component.permissions = [{ subFeatureId: 1, CAN_VIEW: true }];

    pagePanelService.checkTablePermissions.and.returnValue(true);

    const result = component.checkTablePermissions(tableId);

    expect(pagePanelService.checkTablePermissions).toHaveBeenCalledWith(tableId, component.permissions, null);
    expect(result).toBe(true);
  });

  it('should update breadcrumbs correctly', () => {
    const accountName = 'Test Account';
    const breadcrumbService = TestBed.inject(BreadcrumbService) as jasmine.SpyObj<BreadcrumbService>;
    
    component.updateBreadcrumbs(accountName);
    
    expect(breadcrumbService.setBreadcrumbs).toHaveBeenCalledWith([
      { label: 'Managed Accounts', url: '/managed-accounts' },
      { label: accountName }
    ]);
  });

  it('should redirect to account list', () => {
    component.redirectToAccountList();
    
    expect(router.navigate).toHaveBeenCalledWith(['/managed-accounts']);
  });

  it('should open account facts', () => {
    component.openAccountFacts();
    
    expect(component.isAccountFactsOpen).toBe(true);
  });

  it('should open commentaries', () => {
    component.openCommentaries();
    
    expect(component.isAccountFactsOpen).toBe(false);
  });

  it('should open performance data', () => {
    component.openPerformanceData();
    
    expect(component.isAccountFactsOpen).toBe(false);
  });

  it('should select tab correctly', () => {
    const mockTab = { tabId: 2, aliasName: 'Test Tab' };
    component.selectTab(mockTab);
    
    expect(component.selectedTab).toBe(mockTab.tabId);
    expect(component.selectedTabData).toBe(mockTab);
  });

  it('should handle tab selection for performance data', () => {
    const mockEvent = { index: 1 };
    component.performanceTabs = [
      { param: 'NAV_Distribution', aliasName: 'NAV Distribution' },
      { param: 'PE_Performance_Indicators', aliasName: 'PE Performance Indicators' }
    ];
    
    component.onTabSelect(mockEvent);
    
    expect(component.selectedTabIndex).toBe(mockEvent.index);
    expect(component.selectedPerformanceTab).toBe('PE_Performance_Indicators');
  });

  it('should toggle edit mode for commentary', () => {
    const mockCommentary = { 
      commentaryType: 'GLI Commentary', 
      isEdit: false 
    };
    component.canEditGLICommentry = true;
    
    component.toggleEdit(mockCommentary);
    
    expect(mockCommentary.isEdit).toBe(true);
  });

  it('should show no access error when trying to edit without permission', () => {
    const mockCommentary = { 
      commentaryType: 'GLI Commentary', 
      isEdit: false 
    };
    component.canEditGLICommentry = false;
    const toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    
    component.toggleEdit(mockCommentary);
    
    expect(toastrService.error).toHaveBeenCalled();
    expect(mockCommentary.isEdit).toBe(false);
  });

  it('should check permission access correctly', () => {
    const permissions = [
      { CAN_VIEW: true, CAN_EDIT: false },
      { CAN_VIEW: false, CAN_EDIT: true }
    ];
    
    const canView = component.checkPermissionAccess(permissions, 'CAN_VIEW');
    const canEdit = component.checkPermissionAccess(permissions, 'CAN_EDIT');
    
    expect(canView).toBe(true);
    expect(canEdit).toBe(true);
  });

  it('should show no access error', () => {
    const toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    
    component.showNoAccessError();
    
    expect(toastrService.error).toHaveBeenCalled();
  });
  it('should handle empty value check correctly', () => {
    expect(component.isEmpty('')).toBe(true);
    expect(component.isEmpty(null)).toBe(true);
    expect(component.isEmpty(undefined)).toBe(true);
    expect(component.isEmpty('Some text')).toBe(false);
    expect(component.isEmpty('<p>HTML text</p>')).toBe(false);
  });

  it('should redirect to edit page when user has permission', () => {
    component.canEditAccountFacts = true;
    component.managedAccountId = '1';
    
    component.redirectToEditPage(1);
    
    expect(router.navigate).toHaveBeenCalledWith(['/add-managed-account', '1'], {
      queryParams: { step: 1 }
    });
  });
});
