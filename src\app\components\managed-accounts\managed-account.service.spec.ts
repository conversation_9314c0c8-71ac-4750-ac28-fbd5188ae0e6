import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ManagedAccountService } from './managed-account.service';
import { ManagedAccount } from './managed-account.model';
import { environment } from 'src/environments/environment';

describe('ManagedAccountService', () => {
  let service: ManagedAccountService;
  let httpMock: HttpTestingController;
  let baseUrl: string;

  const mockManagedAccount: ManagedAccount = {
    managedAccountName: 'Test Account',
    domicile: 'Test Domicile',
    commencementDate: new Date('2023-01-01'),
    investmentPeriodEndDate: '2025-01-01',
    maturityDate: '2028-01-01',
    commitmentOutstanding: '1000000',
    baseCurrency: 'USD',
    investmentManager: 'Test Manager',
    administrator: 'Test Administrator',
    custodian: 'Test Custodian',
    legalCounsel: 'Test Legal',
    lei: 'TEST123456789',
    investmentSummary: 'Test summary'
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ManagedAccountService]
    });
    service = TestBed.inject(ManagedAccountService);
    httpMock = TestBed.inject(HttpTestingController);
    baseUrl = environment.apiBaseUrl;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('emitGoToStep', () => {
    it('should emit step value', (done) => {
      const testStep = 2;
      
      service.goToStep$.subscribe(step => {
        expect(step).toBe(testStep);
        done();
      });

      service.emitGoToStep(testStep);
    });
  });

  describe('saveManagedAccount', () => {
    it('should save managed account successfully', () => {
      const mockResponse = { id: 1, message: 'Success' };
      
      service.saveManagedAccount(mockManagedAccount).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts/add`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockManagedAccount);
      req.flush(mockResponse);
    });

    it('should handle error when saving managed account', () => {
      const mockError = { status: 400, statusText: 'Bad Request' };
      
      service.saveManagedAccount(mockManagedAccount).subscribe({
        next: () => fail('should have failed with 400 error'),
        error: (error) => {
          expect(error.status).toBe(400);
        }
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts/add`);
      req.flush('Bad Request', mockError);
    });
  });

  describe('getManagedAccountById', () => {
    it('should get managed account by id successfully', () => {
      const accountId = '1';
      const mockResponse = { ...mockManagedAccount, managedAccountId: accountId };
      
      service.getManagedAccountById(accountId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts/${accountId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle error when getting managed account by id', () => {
      const accountId = '999';
      const mockError = { status: 404, statusText: 'Not Found' };
      
      service.getManagedAccountById(accountId).subscribe({
        next: () => fail('should have failed with 404 error'),
        error: (error) => {
          expect(error.status).toBe(404);
        }
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts/${accountId}`);
      req.flush('Not Found', mockError);
    });
  });

  describe('getAllManagedAccounts', () => {
    it('should get all managed accounts successfully', () => {
      const mockResponse = [
        { id: 1, name: 'Account 1' },
        { id: 2, name: 'Account 2' }
      ];
      
      service.getAllManagedAccounts().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle error when getting all managed accounts', () => {
      const mockError = { status: 500, statusText: 'Internal Server Error' };
      
      service.getAllManagedAccounts().subscribe({
        next: () => fail('should have failed with 500 error'),
        error: (error) => {
          expect(error.status).toBe(500);
        }
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts`);
      req.flush('Internal Server Error', mockError);
    });
  });

  describe('deleteManagedAccount', () => {
    it('should delete managed account successfully', () => {
      const accountId = 1;
      const mockResponse = { message: 'Deleted successfully' };
      
      service.deleteManagedAccount(accountId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts/delete/${accountId}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(mockResponse);
    });

    it('should handle error when deleting managed account', () => {
      const accountId = 999;
      const mockError = { status: 403, statusText: 'Forbidden' };
      
      service.deleteManagedAccount(accountId).subscribe({
        next: () => fail('should have failed with 403 error'),
        error: (error) => {
          expect(error.status).toBe(403);
        }
      });

      const req = httpMock.expectOne(`${baseUrl}api/managed-accounts/delete/${accountId}`);
      req.flush('Forbidden', mockError);
    });
  });

  describe('errorHandler', () => {
    it('should return throwError with the error', () => {
      const testError = new Error('Test error');
      
      const result = service['errorHandler'](testError);
      
      result.subscribe({
        next: () => fail('should have thrown error'),
        error: (error) => {
          expect(error).toBe(testError);
        }
      });
    });
  });
});
