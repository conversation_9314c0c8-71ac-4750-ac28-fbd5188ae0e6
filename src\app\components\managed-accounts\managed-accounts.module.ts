import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ManagedAccountsComponent } from './managed-accounts.component';
import { RouterModule } from '@angular/router';
import { NoDataContainerComponent } from './no-data-container/no-data-container.component';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [
    ManagedAccountsComponent,
    NoDataContainerComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild([
        { path: '', component: ManagedAccountsComponent}
    ]),
  ],
  exports: [
    ManagedAccountsComponent
  ]
})
export class ManagedAccountsModule { }
