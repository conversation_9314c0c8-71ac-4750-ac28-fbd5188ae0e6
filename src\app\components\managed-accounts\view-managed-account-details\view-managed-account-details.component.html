<div class="comp-data">
  <div class="company-name-detail">{{ accountData?.administrator }}</div>
  <div class="company-facts-tab">
    <ng-container *ngFor="let tab of tabs">
      <button id="{{tab.tabId}}" 
              [ngClass]="{ highlight: selectedTab === tab.tabId }"
              class="clo-fs-tab"
              (click)="selectTab(tab)">
        {{tab.aliasName}}
      </button>
    </ng-container>
  </div>
  
  <div *ngIf="selectedTab === TAB_NAMES.Investment_Page" class="company-facts-invest-summary">
    <div style="padding-bottom: 20px;">
      <ng-container *ngFor="let table of selectedTabData?.tableList">
        <div *ngIf="(table.tableId==accountFactsTableId  || table.tableId== summaryTableId)"> 
   
          <div class="company-facts-staticDataContainer"> 
            <div *ngIf="canViewAccountFacts && table.tableId==accountFactsTableId"> 
              <div class="company-facts">
                <span class="company-facts-section">{{table.aliasName}}</span>
                <span class="edit-icon" id="edit-account-summary" (click)="redirectToEditPage(1)" (keypress)="redirectToEditPage(1)">
                  <img alt="" src="assets/dist/images/clo_edit.svg" />
                </span>
              </div>
              <div class="container-fluid p-4 custom-border">
                <div class="row">
                  <!-- Left Column -->
                  <div class="col-md-6">
                    <div class="card h-100 border-0">
                      <div class="card-body">
                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Account Name</div>
                          <div class="col-8 text-truncate"
                               [title]="accountData?.managedAccountName?.length > 35 ? accountData.managedAccountName : ''">
                            {{accountData?.managedAccountName}}
                          </div>
                        </div>
                        
                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Domicile</div>
                          <div class="col-8 text-truncate"
                               [title]="accountData?.domicile?.length > 35 ? accountData.domicile : ''">
                            {{accountData?.domicile}}
                          </div>
                        </div>
              
                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Commencement Date</div>
                          <div class="col-8 text-truncate">{{accountData?.commencementDate | date:'shortDate'}}</div>
                        </div>
              
                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Investment Period End</div>
                          <div class="col-8 text-truncate">{{accountData?.investmentPeriodEndDate}}</div>
                        </div>
              
                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Maturity Date</div>
                          <div class="col-8 text-truncate">{{accountData?.maturityDate}}</div>
                        </div>

                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Commitment Outstanding</div>
                          <div class="col-8 text-truncate">
                            {{accountData?.commitmentOutstanding}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              
                  <!-- Right Column -->
                  <div class="col-md-6">
                    <div class="card h-100 border-0">
                      <div class="card-body">
                    
                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Base Currency</div>
                          <div class="col-8 text-truncate">
                            {{accountData?.baseCurrency}}
                          </div>
                        </div>

                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Investment Manager</div>
                          <div class="col-8 text-truncate"
                               [title]="accountData?.investmentManager?.length > 35 ? accountData.investmentManager : ''">
                            {{accountData?.investmentManager}}
                          </div>
                        </div>

                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Administrator</div>
                          <div class="col-8 text-truncate"
                               [title]="accountData?.administrator?.length > 35 ? accountData.administrator : ''">
                            {{accountData?.administrator}}
                          </div>
                        </div>

                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Custodian</div>
                          <div class="col-8 text-truncate"
                               [title]="accountData?.custodian?.length > 35 ? accountData.custodian : ''">
                            {{accountData?.custodian}}
                          </div>
                        </div>

                        <div class="row mb-2">
                          <div class="col-4 text-secondary">Legal Counsel</div>
                          <div class="col-8 text-truncate"
                               [title]="accountData?.legalCounsel?.length > 35 ? accountData.legalCounsel : ''">
                            {{accountData?.legalCounsel}}
                          </div>
                        </div>
                        <div class="row mb-2">
                          <div class="col-4 text-secondary">LEI</div>
                          <div class="col-8 text-truncate"
                               [title]="accountData?.lei?.length > 35 ? accountData.lei : ''">
                            {{accountData?.lei}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Investment Summary Section -->
            <div *ngIf="canViewInvestmentSummary && table.tableId== summaryTableId">
              <div class="company-facts">
                <span class="company-facts-section">{{table.aliasName}}</span>
                <span id="edit-investment-summary" class="edit-icon" (click)="redirectToEditPage(2)" (keypress)="redirectToEditPage(2)"><img alt="" src="assets/dist/images/clo_edit.svg" /></span>
              </div>
              <div class="investment-summary">{{ accountData.investmentSummary }}</div>
          </div>
          </div>
        </div>
        <div *ngIf="(table.tableId!=accountFactsTableId && table.tableId!= summaryTableId)" class="company-facts-staticDataContainer investment-summary-no-borders">
          <div class="company-facts-glo-container">
            <app-flat-table [isShouldFetchDataOnLoad]="true"
            [companyID]="accountData.id"
            [tableTitle]="table.aliasName"
            [tableName]="table.tableName"
            [isStaticTable] = "table.isStaticTable"
            [tableType]="table.tableType"
            [tableId]="table.tableId"
            [canImport] = "checkTablePermissions(table.tableId,CAN_IMPORT)"
            [canExport] = "checkTablePermissions(table.tableId,CAN_EXPORT)"
            [canEdit] = "checkTablePermissions(table.tableId,CAN_EDIT)"
            [isCompositeRowFilterRequired]="table.tableId==Aggregate_CLO_Metrics?true:false">
            </app-flat-table>
          </div> 
        </div>
    
      </ng-container>
    </div>
  </div>

  <!-- Track Record Data Tab -->
  <div *ngIf="selectedTab === TAB_NAMES.Track_Record" class="performance-data-container">
    <div class="container-fluid p-4">
      <div class="row">
        <div class="col-12">
          <kendo-tabstrip (tabSelect)="onTabSelect($event)">
            <kendo-tabstrip-tab *ngFor="let tab of performanceTabs; let i = index" 
                               [title]="tab.aliasName"
                               [selected]="i === selectedTabIndex">
              <div class="performance-tab-content">
                <div *ngIf="checkTabPermission(tab.param) && tab.param === 'Performance' && tab.tableId" class="performance-data">
                  <app-flat-table 
                    [isShouldFetchDataOnLoad]="false"
                    [companyID]="0"
                    [tableTitle]="tab.aliasName"
                    [tableName]="tab.aliasName"
                    [isStaticTable]="false"
                    [tableType]="'regular'"
                    [tableId]="tab.tableId"
                    [canImport]="true"
                    [canExport]="true"
                    [canEdit]="true"
                    [isCompositeRowFilterRequired]="false">
                  </app-flat-table>
                </div>
                <div *ngIf="checkTabPermission(tab.param) && tab.param !== 'Performance'" class="performance-data">
                  <!-- Other performance data content will go here -->
                  <p>Performance data for {{tab.aliasName}} will be displayed here.</p>
                </div>
                <div *ngIf="!checkTabPermission(tab.param)" class="no-access">
                  <p>You don't have permission to view this data.</p>
                </div>
              </div>
            </kendo-tabstrip-tab>
          </kendo-tabstrip>
        </div>
      </div>
    </div>
  </div>

  <!-- Commentaries Tab -->
  <div *ngIf="selectedTab === TAB_NAMES.Commentaries" class="commentaries-container">
    <div class="container-fluid p-4">
      <div class="row">
        <div class="col-12">
          <div class="commentaries-list">
            <div *ngFor="let commentary of commentarylist" class="commentary-item">
              <div class="commentary-header" (click)="expandPanel(commentary)">
                <div class="commentary-title">
                  <span class="commentary-name">{{commentary.name}}</span>
                  <span class="commentary-sequence">({{commentary.sequenceNo}})</span>
                </div>
                <div class="commentary-actions">
                  <button *ngIf="commentary.commentaryType === 'GLI Commentary' ? canEditGLICommentry : canEditMarketCommentry"
                          class="btn btn-sm btn-outline-primary"
                          (click)="toggleEdit(commentary)">
                    {{commentary.isEdit ? 'Cancel' : 'Edit'}}
                  </button>
                  <span class="expand-icon">{{commentary.isExpanded ? '▼' : '▶'}}</span>
                </div>
              </div>
              
              <div *ngIf="commentary.isExpanded" class="commentary-content">
                <div *ngIf="!commentary.isEdit" class="commentary-display">
                  <div [innerHTML]="commentary.newComment || 'No commentary available.' | safeHtml"></div>
                </div>
                
                <div *ngIf="commentary.isEdit" class="commentary-edit">
                  <div class="form-group">
                    <textarea class="form-control" 
                              rows="6" 
                              [(ngModel)]="commentary.newComment"
                              (input)="updateCharCount($event)"
                              placeholder="Enter your commentary here..."></textarea>
                    <div class="char-count">Characters: {{charCount}}</div>
                  </div>
                  <div class="commentary-edit-actions">
                    <button class="btn btn-primary" (click)="onSave(commentary, commentary.commentaryType)">
                      Save
                    </button>
                    <button class="btn btn-secondary" (click)="onCancel(commentary)">
                      Cancel
                    </button>
                    <button class="btn btn-outline-secondary" (click)="onReset(commentary)">
                      Reset
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
</div>