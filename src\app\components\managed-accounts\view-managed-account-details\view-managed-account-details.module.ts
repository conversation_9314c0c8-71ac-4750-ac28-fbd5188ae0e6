import { ViewManagedAccountDetailsComponent } from './view-managed-account-details.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { SharedCloModule } from 'src/app/components/clo/shared-clo.module';
import { SafeHtmlPipe } from '../shared/safe-html.pipe';

@NgModule({
  declarations: [
    ViewManagedAccountDetailsComponent,
    SafeHtmlPipe
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    SharedComponentModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    SharedDirectiveModule,
    QuillModule,
    RouterModule.forChild([
      { path: '', component: ViewManagedAccountDetailsComponent }
    ]),
    KendoModule,
    SharedCloModule
  ],
  exports: [
    ViewManagedAccountDetailsComponent,
    SafeHtmlPipe
  ],
  providers: []
})
export class ViewManagedAccountDetailsComponentModule { }
