import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ManagedAccount } from '../managed-account.model';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
import { ManagedAccountService } from '../managed-account.service';

@Component({
  selector: 'app-add-managed-account',
  templateUrl: './add-managed-account.component.html',
  styleUrls: ['./add-managed-account.component.scss'],
  providers: [DatePipe]
})
export class AddManagedAccountComponent implements OnInit {
  managedAccountForm: FormGroup;
  step: number = 1;
  savedData: ManagedAccount;
  isLoading: boolean = false;
  MILLISECONDS_PER_MINUTE: number = 60000;
  SUMMARY_COUNT_FIELD: string = 'SummaryCount';


  resetText: string = "Reset";
  cancelText: string = "Cancel";
  saveText: string = "Save";
  charCount: number = 0;
  isAccountFactsSaved: boolean = false;
  isEdited: boolean = false;
  isFromReviewPage: boolean = false;
  isSummarySaved: boolean = false;
  isSummaryEdited: boolean = false;
  submitted: boolean = false;
  showPopup: boolean = false;
  resetSummaryOnly = false;
  errorMessage: string = "Please fill all the missing fields.";
  managedAccountId: string = '';

  constructor(
    private readonly fb: FormBuilder,
    private readonly router: Router,
    private datePipe: DatePipe,
    private readonly toastrService: ToastrService,
    private route: ActivatedRoute,
    private breadcrumbService: BreadcrumbService,
    private readonly managedAccountService: ManagedAccountService
  ) {
    this.route.paramMap?.subscribe(params => {
      this.managedAccountId = params.get('id');       
    });
  }

  ngOnInit(): void {
    this.updateBreadcrumbs();
    this.managedAccountForm = this.fb.group({
      managedAccountName: ['', Validators.required],
      domicile: ['', Validators.required],
      commencementDate: ['', Validators.required],
      investmentPeriodEndDate: ['', Validators.required],
      maturityDate: ['', Validators.required],
      commitmentOutstanding: ['', Validators.required],
      baseCurrency: ['', Validators.required],
      investmentManager: ['', Validators.required],
      administrator: ['', Validators.required],
      custodian: ['', Validators.required],
      legalCounsel: ['', Validators.required],
      lei: ['', Validators.required],
      SummaryCount: ['', Validators.required],
    });

    this.managedAccountForm.valueChanges.subscribe(() => {
      this.isEdited = true;
    });

    // Get step from route query params
    this.route.queryParams.subscribe(params => {
      if (params['step']) {
        const stepNumber = parseInt(params['step']);
        if (stepNumber >= 1 && stepNumber <= 3) {
          this.goToStep(stepNumber);
          this.isEdited = true;
          this.isFromReviewPage = true;
        }
      }
    });

    // Keep the existing subscription for other cases
    this.managedAccountService.goToStep$.subscribe(step => {
      this.goToStep(step);
      this.isEdited = true;
      this.isFromReviewPage = true;
    });

    if(this.managedAccountId && this.managedAccountId != '') {
      this.getManagedAccountById(this.managedAccountId);
    }
  }

  updateBreadcrumbs() {
    let newBreadcrumbs: any[] = [];
    let message = this.managedAccountId === '' ? 'Add Managed Account' : 'Update Managed Account'

    newBreadcrumbs.push({ label: 'Managed Accounts', url: '/managed-accounts' });
    newBreadcrumbs.push({ label: message });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  }

  goToStep(step: number): void {
    if(this.isSummarySaved){
      this.isEdited = true;
    }

    this.step = step;
  }

  onCancel(): void {
    this.router.navigate(['/managed-accounts']); 
  }

  onCloseDialog(): void {
    this.showPopup = false;
    this.router.navigate(['/managed-accounts']); 
  }

  updateCharCount(): void {
    const summaryCountValue = this.managedAccountForm.get(this.SUMMARY_COUNT_FIELD)?.value || '';
    this.charCount = summaryCountValue.length;
    this.isSummaryEdited = true;
  }

  setCurrentDate(controlName: string): void {
    const control = this.managedAccountForm.get(controlName);
    if (control && !control.value) {
      control.setValue(new Date());
    }
  }

  onSubmit(): void {
    const controls = this.managedAccountForm.controls;
    let allFieldsFilled = true;
    this.submitted = true;

    for (const name in controls) {
      if (name !== this.SUMMARY_COUNT_FIELD) {
        const control = controls[name];
        const value = control.value;
        const trimmedValue = (typeof value === 'string') ? value.trim() : value;
        control.setValue(trimmedValue);

        if (!trimmedValue) {
          allFieldsFilled = false;
          break;
        }
      }
    }

    if (!allFieldsFilled) {
      this.managedAccountForm.markAllAsTouched();
      this.toastrService.error(this.errorMessage, "", { positionClass: "toast-center-center" });
      return;
    }

    this.savedData = this.mapFormToModel();    

    if (this.managedAccountId && this.managedAccountId != '') {
      this.saveManagedAccount();
      return;
    }

    if (this.step === 1) {
      this.isAccountFactsSaved = true;
      this.goToStep(this.isFromReviewPage ? 3 : 2);
      this.isFromReviewPage = false;
    } else if (this.step === 2) {
      this.isSummarySaved = true;
      this.goToStep(3);
    }


    this.isEdited = false;
  }
  
  getManagedAccountById(managedAccountId: string): void {
    this.isLoading = true;
    this.managedAccountService.getManagedAccountById(managedAccountId).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data != null) {
          this.managedAccountForm.patchValue({
            managedAccountName: data.managedAccountName,
            domicile: data.domicile,
            commencementDate: this.parseDate(data.commencementDate.toString()),
            investmentPeriodEndDate: data.investmentPeriodEndDate,
            maturityDate: data.maturityDate,
            commitmentOutstanding: data.commitmentOutstanding,
            baseCurrency: data.baseCurrency,
            investmentManager: data.investmentManager,
            administrator: data.administrator,
            custodian: data.custodian,
            legalCounsel: data.legalCounsel,
            lei: data.lei,
            SummaryCount: data.investmentSummary,
          });
        }
        else {
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error(error.error.error, "", { positionClass: "toast-center-center" });
      }
    });
  }

  saveManagedAccount(): void {
    this.isLoading = true;  
    
    const saveOperation = (this.managedAccountId === '' || this.managedAccountId === null) 
      ? this.managedAccountService.saveManagedAccount(this.savedData)
      : this.managedAccountService.updateManagedAccount(this.managedAccountId,this.savedData);

    saveOperation.subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data.id) {
          this.toastrService.success(data.message, "", { positionClass: "toast-center-center" });
          this.router.navigate(['/managed-accounts']);
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error(error.error.error, "", { positionClass: "toast-center-center" });
      }
    });
}

  private mapFormToModel(): ManagedAccount {
    const formValues = this.managedAccountForm.value;
    const managedAccountModel = {} as ManagedAccount;
    managedAccountModel.id = this.managedAccountId;
    managedAccountModel.managedAccountName = formValues.managedAccountName;
    managedAccountModel.domicile = formValues.domicile;
    managedAccountModel.commencementDate = new Date(this.adjustForTimezone(formValues.commencementDate));
    managedAccountModel.investmentPeriodEndDate = formValues.investmentPeriodEndDate;
    managedAccountModel.maturityDate = formValues.maturityDate;
    managedAccountModel.commitmentOutstanding = formValues.commitmentOutstanding;
    managedAccountModel.baseCurrency = formValues.baseCurrency;
    managedAccountModel.investmentManager = formValues.investmentManager;
    managedAccountModel.administrator = formValues.administrator;
    managedAccountModel.custodian = formValues.custodian;
    managedAccountModel.legalCounsel = formValues.legalCounsel;
    managedAccountModel.lei = formValues.lei;
    managedAccountModel.investmentSummary = formValues.SummaryCount;
    return managedAccountModel;
  }

  private adjustForTimezone(date: Date): number {
    return date.getTime() - (date.getTimezoneOffset() * this.MILLISECONDS_PER_MINUTE);
  }

  private parseDate(dateString: string): Date {
    return new Date(Date.parse(dateString));
  }

  onResetInvestmentSummary(): void {
    this.managedAccountForm.patchValue({
      [this.SUMMARY_COUNT_FIELD]: ''
    });
    this.isEdited = false;
    this.submitted = false;
  }

  onResetAccountFacts(): void {
    Object.keys(this.managedAccountForm.controls).forEach(key => {
      if (key !== this.SUMMARY_COUNT_FIELD) {
        this.managedAccountForm.get(key)?.reset();
      }
    });
    this.isEdited = false;
    this.submitted = false;
  }

  onReset(): void {
    if (this.step === 1) {
      this.onResetAccountFacts();
    } else {
      this.onResetInvestmentSummary();
    }
  }
}
