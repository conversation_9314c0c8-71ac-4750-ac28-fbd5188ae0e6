import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { ManagedAccountsComponent } from './managed-accounts.component';
import { ManagedAccountService } from './managed-account.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
import { NoDataContainerComponent } from './no-data-container/no-data-container.component';

describe('ManagedAccountsComponent', () => {
  let component: ManagedAccountsComponent;
  let fixture: ComponentFixture<ManagedAccountsComponent>;
  let mockManagedAccountService: jasmine.SpyObj<ManagedAccountService>;
  let mockBreadcrumbService: jasmine.SpyObj<BreadcrumbService>;
  let mockRouter: jasmine.SpyObj<Router>;

  const mockManagedAccounts = [
    { id: 1, name: 'Test Account 1' },
    { id: 2, name: 'Test Account 2' }
  ];

  beforeEach(async () => {
    const managedAccountServiceSpy = jasmine.createSpyObj('ManagedAccountService', ['getAllManagedAccounts']);
    const breadcrumbServiceSpy = jasmine.createSpyObj('BreadcrumbService', ['setBreadcrumbs']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [ 
        ManagedAccountsComponent,
        NoDataContainerComponent
      ],
      providers: [
        { provide: ManagedAccountService, useValue: managedAccountServiceSpy },
        { provide: BreadcrumbService, useValue: breadcrumbServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    mockManagedAccountService = TestBed.inject(ManagedAccountService) as jasmine.SpyObj<ManagedAccountService>;
    mockBreadcrumbService = TestBed.inject(BreadcrumbService) as jasmine.SpyObj<BreadcrumbService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManagedAccountsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.gridData).toEqual([]);
    expect(component.showNoData).toBeFalse();
    expect(component.showForm).toBeFalse();
    expect(component.showPopup).toBeFalse();
    expect(component.canAddManagedAccount).toBeTrue();
    expect(component.canDeleteManagedAccount).toBeTrue();
    expect(component.managedAccountName).toBe('');
    expect(component.id).toBe(-1);
    expect(component.isLoading).toBeFalse();
    expect(component.managedAccountFilter).toBe('');
  });

  it('should call updateBreadcrumbs in constructor', () => {
    expect(mockBreadcrumbService.setBreadcrumbs).toHaveBeenCalledWith([
      { label: 'Managed Accounts', url: '/managed-accounts' }
    ]);
  });

  it('should call loadManagedAccountsList on ngOnInit', () => {
    spyOn(component, 'loadManagedAccountsList');
    component.ngOnInit();
    expect(component.loadManagedAccountsList).toHaveBeenCalled();
  });

  describe('loadManagedAccountsList', () => {
    it('should load managed accounts successfully', fakeAsync(() => {
      mockManagedAccountService.getAllManagedAccounts.and.returnValue(of(mockManagedAccounts));
      
      component.loadManagedAccountsList();
      tick();

      expect(component.isLoading).toBeFalse();
      expect(component.gridData).toEqual(mockManagedAccounts);
      expect(component.showNoData).toBeFalse();
    }));

    it('should handle error when loading managed accounts', fakeAsync(() => {
      mockManagedAccountService.getAllManagedAccounts.and.returnValue(throwError(() => new Error('API Error')));
      
      component.loadManagedAccountsList();
      tick();

      expect(component.isLoading).toBeFalse();
      expect(component.showNoData).toBeTrue();
    }));

    it('should show no data when gridData is empty', fakeAsync(() => {
      mockManagedAccountService.getAllManagedAccounts.and.returnValue(of([]));
      
      component.loadManagedAccountsList();
      tick();

      expect(component.isLoading).toBeFalse();
      expect(component.gridData).toEqual([]);
      expect(component.showNoData).toBeTrue();
    }));
  });

  describe('addRedirect', () => {
    it('should navigate to add managed account when user has permission', () => {
      component.canAddManagedAccount = true;
      
      component.addRedirect();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/add-managed-account']);
    });

    it('should show no access error when user does not have permission', () => {
      spyOn(component, 'showNoAccessError');
      component.canAddManagedAccount = false;
      
      component.addRedirect();

      expect(component.showNoAccessError).toHaveBeenCalled();
      expect(mockRouter.navigate).not.toHaveBeenCalled();
    });
  });

  describe('redirectToAccountData', () => {
    it('should handle redirect to account data', () => {
      const accountId = '123';
      
      component.redirectToAccountData(accountId);

      // Currently this method is commented out, so we just verify it doesn't throw
      expect(true).toBeTrue();
    });
  });

  describe('showDeletePopup', () => {
    it('should show delete popup when user has permission', () => {
      const accountId = 123;
      const accountName = 'Test Account';
      component.canDeleteManagedAccount = true;
      
      component.showDeletePopup(accountId, accountName);

      expect(component.showPopup).toBeTrue();
      expect(component.managedAccountName).toBe(accountName);
      expect(component.id).toBe(accountId);
    });

    it('should show no access error when user does not have permission', () => {
      spyOn(component, 'showNoAccessError');
      component.canDeleteManagedAccount = false;
      
      component.showDeletePopup(123, 'Test Account');

      expect(component.showNoAccessError).toHaveBeenCalled();
      expect(component.showPopup).toBeFalse();
    });
  });

  describe('showNoAccessError', () => {
    it('should log no access message', () => {
      spyOn(console, 'log');
      
      component.showNoAccessError();

      expect(console.log).toHaveBeenCalledWith('No access to perform this action');
    });
  });

  describe('AccountCount getter', () => {
    it('should return correct account count', () => {
      component.gridData = mockManagedAccounts;
      
      expect(component.AccountCount).toBe(2);
    });

    it('should return 0 when gridData is empty', () => {
      component.gridData = [];
      
      expect(component.AccountCount).toBe(0);
    });
  });

  describe('formatAccountName', () => {
    it('should return original name when no filter is applied', () => {
      const accountName = 'Test Account';
      component.managedAccountFilter = '';
      
      const result = component.formatAccountName(accountName);
      
      expect(result).toBe(accountName);
    });

    it('should highlight filtered text when filter is applied', () => {
      const accountName = 'Test Account';
      component.managedAccountFilter = 'Test';
      
      const result = component.formatAccountName(accountName);
      
      expect(result).toBe('<strong>Test</strong> Account');
    });

    it('should handle case insensitive filtering', () => {
      const accountName = 'Test Account';
      component.managedAccountFilter = 'test';
      
      const result = component.formatAccountName(accountName);
      
      expect(result).toBe('<strong>Test</strong> Account');
    });
  });

  describe('clearSearch', () => {
    it('should clear the managed account filter', () => {
      component.managedAccountFilter = 'test filter';
      
      component.clearSearch();
      
      expect(component.managedAccountFilter).toBe('');
    });
  });
});
