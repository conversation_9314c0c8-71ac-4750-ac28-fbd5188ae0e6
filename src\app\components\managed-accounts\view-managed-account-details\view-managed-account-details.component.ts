import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { ManagedAccount } from '../managed-account.model';
import { TabStripComponent } from "@progress/kendo-angular-layout";
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { FeatureTableMapping, ManagedAccountFeatureTableMapping, TOASTER_MSG } from 'src/app/common/constants';
import { CommonSubFeaturePermissionService } from 'src/app/services/subPermission.service';
import { PermissionActions } from 'src/app/common/constants';
import { ErrorMessage } from 'src/app/services/miscellaneous.service';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { PanelbarItemService } from '../../clo/shared/panelbar-item/panelbar-item.service';
import { BreadcrumbService } from 'src/app/services/breadcrumb-service.service';
import { Subscription } from 'rxjs';
import { ManagedAccountService } from '../managed-account.service';

@Component({
  selector: 'app-view-managed-account-details',
  templateUrl: './view-managed-account-details.component.html',
  styleUrls: ['./view-managed-account-details.component.scss']
})
export class ViewManagedAccountDetailsComponent implements OnInit {
  public TAB_NAMES = {
    Investment_Page: 1,
    Track_Record: 2,
    Portfolio_Statistics: 2,
    Investment_Portfolio: 3,
    Investor_Cashflow_Activity: 7,
    Commentaries: 8,
    Return_Analysis: 9,
    Return_Composition: 10,
    Currency_Exposure: 11,
  };

  @ViewChild('quillEditor', { static: false }) quillEditor: ElementRef;
  @ViewChild('tabStrip') public tabStrip: TabStripComponent;

  pageId: number = 1;
  CAN_IMPORT = PermissionActions.CAN_IMPORT;
  CAN_EXPORT = PermissionActions.CAN_EXPORT;
  CAN_EDIT = PermissionActions.CAN_EDIT;
  subscription: Subscription;
  isLoading: boolean = true;
  managedAccountId: string;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private readonly toastrService: ToastrService,
    private managedAccountService: ManagedAccountService,
    private el: ElementRef,
    private sanitizer: DomSanitizer,
    private subPermissionService: CommonSubFeaturePermissionService,
    private pagePanelService: PanelbarItemService,
    private breadcrumbService: BreadcrumbService
  ) { }

  tabs: any = [];
  isAccountFactsOpen: boolean = true;
  selectedTab: number = this.TAB_NAMES.Investment_Page;
  accountName: string = '';
  accountData: ManagedAccount;
  public accountNames: any[] = [];
  showTextbox: boolean = false;
  charCount: number = 0;
  isEdit: boolean = false;
  investmentPage: boolean = false;
  performancePage: boolean = false;
  selectedPerformanceTab: string = 'NAV_Distribution';
  editingCommentaryId: number | null = null;
  savedComments: { [key: string]: string } = {};

  commentarylist: { tableId: number, sequenceNo: number, id: number, name: string, newComment: string, commentaryType: string, glicommentry?: string, marketCommentry?: string, isExpanded: boolean, isEdit: boolean }[] = [
    { tableId: 24, id: 0, sequenceNo: 1, name: 'GLI Commentary', newComment: '', commentaryType: 'GLI Commentary', glicommentry: '', marketCommentry: '', isExpanded: false, isEdit: false },
    { tableId: 25, id: 0, sequenceNo: 2, name: 'Market Commentary - Global loans and CLOs', newComment: '', commentaryType: 'Market Commentary - Global loans and CLOs', glicommentry: '', marketCommentry: '', isExpanded: false, isEdit: false },
  ];

  performanceData: any[] = [];
  performanceColumns: any[] = [];
  public selectedTabIndex = 0;
  public performanceTabs: any[] = [];
  selectedTabData: any;

  accountFactsTableId = ManagedAccountFeatureTableMapping.TABLES_NAME.Managed_Account_Facts;
  summaryTableId = ManagedAccountFeatureTableMapping.TABLES_NAME.Investment_Summary;

  ngOnInit() {
    this.selectedTab = this.TAB_NAMES.Investment_Page;
    this.isLoading = true;
    
    this.route.paramMap.subscribe(params => {
      this.managedAccountId = params.get('id');
      this.getSubFeatureAccessPermissions();
      this.redirectToAccountData(this.managedAccountId);
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  updateBreadcrumbs(accountName: string) {
    let newBreadcrumbs: any[] = [];
    newBreadcrumbs.push({ label: 'Managed Accounts', url: '/managed-accounts' });
    newBreadcrumbs.push({ label: accountName });
    this.breadcrumbService.setBreadcrumbs(newBreadcrumbs);
  }

  getConfiguration(){
    this.isLoading = true;
    this.managedAccountService.getTabList(this.pageId,this.managedAccountId).subscribe((data:any)=>{
      let tempTabs=data;     
      this.tabs = tempTabs?.map((x) =>
        Object.assign(x, {
          onClick:"() => {}"
        })
      );
      this.tabs.forEach(page => {
        if(page.tableList){
        this.pagePanelService.updateManagedAccountTableVisibility(page.tableList,this.permissions);
        }
        page.subTabList?.forEach(tab => {
          if(tab.tableList){
            this.pagePanelService.updateManagedAccountTableVisibility(tab.tableList,this.permissions);
          }
        });
    });
    this.selectedTabData=this.tabs.find(x=>x.tabId==this.selectedTab);
      this.tabs?.forEach(element => {
        if(element.tabId==this.TAB_NAMES.Investment_Page){
          element.onClick="() => this.openCompanyFacts()";
        }
        else if(element.tabId==this.TAB_NAMES.Commentaries){
          element.onClick="() => this.openCommentaries()";
          element.tableList.forEach(el=>{
           this.commentarylist?.forEach(com=>{
            if(com.tableId==el.tableId){
              com.name=el.aliasName;
              com.sequenceNo=el.sequenceNo;
            }
           })
          })
        }
        else if(element.tabId==this.TAB_NAMES.Track_Record){
          element.onClick="() => this.openPerformanceData()";
          this.performanceTabs=element.subTabList;          
        }
        
      });
      this.isLoading = false;
    });
  }

  redirectToAccountData(managedAccountId: string) {
    this.isLoading = true;
    this.managedAccountService.getManagedAccountById(managedAccountId).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data == null) {
          this.toastrService.error(TOASTER_MSG.NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
          this.router.navigate(['/managed-accounts']);
          return;
        }

        this.accountData = data;
        this.managedAccountId = managedAccountId;
        this.updateBreadcrumbs(this.accountData?.administrator);
        //this.router.navigate(['/view-managed-account-details', this.managedAccountId]);
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error('Failed to load managed account details.', "", { positionClass: "toast-center-center" });
        this.router.navigate(['/managed-accounts']);
      }
    });
  }

  redirectToAccountList() {
    this.router.navigate(['/managed-accounts']);
  }

  openAccountFacts() {
    this.isAccountFactsOpen = true;
  }

  openCommentaries() {
    // this.loadCommentaries();
    this.isAccountFactsOpen = false;
  }

  openPerformanceData() {
    this.isAccountFactsOpen = false;
  }

  highlightTab(tab: number) {
    this.selectedTab = tab;
  }

  redirectToEditPage(step: number) {
    this.router.navigate(['/add-managed-account', this.managedAccountId], {
      queryParams: { step: step }
    });    
  }

//   loadCommentaries() {
//     this.isLoading = true;
//     if (!this.accountData?.id) {
//       return;
//     }
//     // Mock commentary loading - replace with actual service call
//     this.isLoading = false;
//     this.commentarylist.forEach(commentary => {
//       if (commentary.commentaryType === 'GLI Commentary' && this.canViewGLICommentry) {
//         commentary.isExpanded = true;
//       }
//       if (commentary.commentaryType === 'Market Commentary - Global loans and CLOs' && this.canViewMarketCommentry) {
//         commentary.isExpanded = true;
//       }
//     });
//   }

  isEmpty(val) {
    val = val?.replace(/<.*?>/g, '');
    return (val === undefined || val == null || val.length <= 0) ? true : false;
  }

  onSave(clo: any, commentaryType: string) {
    this.isLoading = true;
    // Mock save functionality - replace with actual service call
    setTimeout(() => {
      this.isLoading = false;
      this.toastrService.success('Commentary saved successfully.', "", { positionClass: "toast-center-center" });
      clo.isEdit = false;
      // this.loadCommentaries();
    }, 1000);
  }

  onCancel(clo: any): void {
    // this.loadCommentaries();
    clo.isEdit = false;
  }

  selectTab(tab: any) {
    this.selectedTab = tab.tabId;
    this.selectedTabData = tab;
    if (this.selectedTab !== this.TAB_NAMES.Commentaries) {
      this.showTextbox = false;
    }
  }

  onTabSelect(e: any): void {
    this.selectedTabIndex = e.index;
    this.selectedPerformanceTab = this.performanceTabs[e.index].param;
  }

  toggleEdit(clo: any) {
    const hasEditPermission = clo.commentaryType === 'GLI Commentary' ? this.canEditGLICommentry : this.canEditMarketCommentry;

    if (!hasEditPermission) {
      this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
      return;
    }

    clo.isEdit = !clo.isEdit;
  }

  updateCharCount(event: any) {
    this.charCount = event.target.value.length;
  }

  expandPanel(clo: any) {
    this.commentarylist.forEach(item => {
      if (item !== clo) {
        item.isExpanded = false;
        item.isEdit = false;
      }
    });
    clo.isExpanded = !clo.isExpanded;
  }

  onReset(clo: any): void {
    this.charCount = 0;
    clo.newComment = '';
  }

  // Permission flags
  canViewAccountFacts: boolean = true;
  canEditAccountFacts: boolean = true;
  canViewInvestmentSummary: boolean = true;
  canAddInvestmentSummary: boolean = true;
  canViewGLIPortfolioComposition: boolean = true;
  canEditGLIPortfolioComposition: boolean = true;
  canExportGLIPortfolioComposition: boolean = true;
  canImportGLIPortfolioComposition: boolean = true;
  canViewAggregateCLOMetrics: boolean = true;
  canEditAggregateCLOMetrics: boolean = true;
  canExportAggregateCLOMetrics: boolean = true;
  canImportAggregateCLOMetrics: boolean = true;
  canViewPEPerformanceIndicators: boolean = true;
  canViewReturnAnalysis: boolean = true;
  canViewReturnComposition: boolean = true;
  canViewCurrencyExposure: boolean = true;
  canViewNAVDistribution: boolean = true;
  canViewGLICommentry: boolean = true;
  canEditGLICommentry: boolean = true;
  canViewMarketCommentry: boolean = true;
  canEditMarketCommentry: boolean = true;
  permissions: any = [];

  getSubFeatureAccessPermissions() {
    this.subPermissionService.getCommonFeatureAccessPermissions(FeaturesEnum.ManagedAccount).subscribe({
      next: (result) => {
        if (result.length > 0) {
          this.permissions = result;
          this.getConfiguration();

          // Set permission flags based on result
        //   this.canViewAccountFacts = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == ManagedAccountFeatureTableMapping.FEATURE_TABLE_MAP.ManagedAccountFacts.FeatureId), PermissionActions.CAN_VIEW);
         // this.canEditAccountFacts = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == ManagedAccountFeatureTableMapping.FEATURE_TABLE_MAP.ManagedAccountFacts.FeatureId), PermissionActions.CAN_EDIT);
         //this.canViewInvestmentSummary = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == ManagedAccountFeatureTableMapping.FEATURE_TABLE_MAP.InvestmentSummary.FeatureId), PermissionActions.CAN_VIEW);
        //   this.canAddInvestmentSummary = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == ManagedAccountFeatureTableMapping.FEATURE_TABLE_MAP.InvestmentSummary.FeatureId), PermissionActions.CAN_EDIT);
        //   this.canViewGLICommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.GLICommentry.FeatureId), PermissionActions.CAN_VIEW);
        //   this.canEditGLICommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.GLICommentry.FeatureId), PermissionActions.CAN_EDIT);
        //   this.canViewMarketCommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.MarketCommentry.FeatureId), PermissionActions.CAN_VIEW);
        //   this.canEditMarketCommentry = this.checkPermissionAccess(result?.filter(x => x.subFeatureId == FeatureTableMapping.FEATURE_TABLE_MAP.MarketCommentry.FeatureId), PermissionActions.CAN_EDIT);

          const checkViewPermission = function () {
            return this.canViewAccountFacts || this.canViewInvestmentSummary;
          };
          const checkPerformancePermission = () => {
            return this.canViewPEPerformanceIndicators || this.canViewReturnAnalysis ||
              this.canViewNAVDistribution || this.canViewReturnComposition || this.canViewCurrencyExposure;
          };
          this.investmentPage = checkViewPermission.call(this);
          this.performancePage = checkPerformancePermission();

          if (!this.investmentPage) {
            this.hideTab(this.TAB_NAMES.Investment_Page);
          }
          if (!this.performancePage) {
            this.hideTab(this.TAB_NAMES.Track_Record);
          }
        //   if (this.accountData?.id) {
        //     this.loadCommentaries();
        //   }
        }
      },
      error: (_error) => {
        // Handle error
      }
    });
  }

  checkPermissionAccess(permission: any[], permissionType): boolean {
    return permission.map(x => x[permissionType]).includes(true);
  }

  showNoAccessError() {
    this.toastrService.error(ErrorMessage.NoAccess, "", { positionClass: "toast-center-center" });
  }

  currentTab(tabId: number) {
    const tab = this.tabs.find(t => t.id === tabId && t.visible);
    if (tab) {
      this.selectedTab = tabId;
      tab.onClick();
    } else {
      this.selectNextVisibleTab();
    }
  }

  selectNextVisibleTab() {
    const nextVisibleTab = this.tabs.find(t => t.visible);
    if (nextVisibleTab) {
      this.selectedTab = nextVisibleTab.id;
      nextVisibleTab.onClick();
    }
  }

  hideTab(tabId: number) {
    const tab = this.tabs.find(t => t.tabId === tabId);
    if (tab) {
      tab.visible = false;
      if (this.selectedTab === tabId) {
        this.selectNextVisibleTab();
      }
    }
  }

//   checkTabPermission(tabParam: number): boolean {
//     switch (tabParam) {
//       case this.TAB_NAMES.Portfolio_Statistics:
//         return this.canViewNAVDistribution;
//       case this.TAB_NAMES.Investment_Portfolio:
//         return this.canViewPEPerformanceIndicators;
//       case this.TAB_NAMES.Return_Analysis:
//         return this.canViewReturnAnalysis;
//       case this.TAB_NAMES.Return_Composition:
//         return this.canViewReturnComposition;
//       case this.TAB_NAMES.Currency_Exposure:
//         return this.canViewCurrencyExposure;
//       default:
//         return false;
//     }
//   }

  checkTablePermissions(tableId: number, permissionType: string = null): boolean {
    return this.pagePanelService.checkTablePermissions(tableId, this.permissions, permissionType);
  }
}
