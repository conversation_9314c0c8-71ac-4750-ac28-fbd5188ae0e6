import { ManagedAccount } from './managed-account.model';

describe('ManagedAccount Model', () => {
  it('should have the correct interface structure', () => {
    const managedAccount: ManagedAccount = {
      managedAccountName: 'Test Account',
      domicile: 'Test Domicile',
      commencementDate: new Date('2023-01-01'),
      investmentPeriodEndDate: '2025-01-01',
      maturityDate: '2028-01-01',
      commitmentOutstanding: '1000000',
      baseCurrency: 'USD',
      investmentManager: 'Test Manager',
      administrator: 'Test Administrator',
      custodian: 'Test Custodian',
      legalCounsel: 'Test Legal',
      lei: 'TEST123456789',
      investmentSummary: 'Test summary'
    };

    expect(managedAccount).toBeDefined();
    expect(typeof managedAccount.managedAccountName).toBe('string');
    expect(typeof managedAccount.domicile).toBe('string');
    expect(managedAccount.commencementDate instanceof Date).toBeTrue();
    expect(typeof managedAccount.investmentPeriodEndDate).toBe('string');
    expect(typeof managedAccount.maturityDate).toBe('string');
    expect(typeof managedAccount.commitmentOutstanding).toBe('string');
    expect(typeof managedAccount.baseCurrency).toBe('string');
    expect(typeof managedAccount.investmentManager).toBe('string');
    expect(typeof managedAccount.administrator).toBe('string');
    expect(typeof managedAccount.custodian).toBe('string');
    expect(typeof managedAccount.legalCounsel).toBe('string');
    expect(typeof managedAccount.lei).toBe('string');
    expect(typeof managedAccount.investmentSummary).toBe('string');
  });

  it('should have all required properties', () => {
    const managedAccount: ManagedAccount = {
      managedAccountName: 'Test Account',
      domicile: 'Test Domicile',
      commencementDate: new Date('2023-01-01'),
      investmentPeriodEndDate: '2025-01-01',
      maturityDate: '2028-01-01',
      commitmentOutstanding: '1000000',
      baseCurrency: 'USD',
      investmentManager: 'Test Manager',
      administrator: 'Test Administrator',
      custodian: 'Test Custodian',
      legalCounsel: 'Test Legal',
      lei: 'TEST123456789',
      investmentSummary: 'Test summary'
    };

    expect(managedAccount.managedAccountName).toBe('Test Account');
    expect(managedAccount.domicile).toBe('Test Domicile');
    expect(managedAccount.commencementDate instanceof Date).toBeTrue();
  });

  it('should handle different data types correctly', () => {
    const managedAccount: ManagedAccount = {
      managedAccountName: 'Complex Account Name with Special Characters!@#$%',
      domicile: 'International Domicile',
      commencementDate: new Date('2020-12-31T23:59:59.999Z'),
      investmentPeriodEndDate: '2030-12-31',
      maturityDate: '2040-12-31',
      commitmentOutstanding: '*********.99',
      baseCurrency: 'EUR',
      investmentManager: 'International Investment Manager Ltd.',
      administrator: 'Global Administrator Services',
      custodian: 'Worldwide Custodian Bank',
      legalCounsel: 'International Legal Counsel LLP',
      lei: '529900W1K3BJ56RGWW37',
      investmentSummary: 'This is a comprehensive investment summary with detailed information about the managed account structure, investment strategy, and risk management approach.'
    };

    expect(managedAccount.managedAccountName).toContain('Complex Account Name');
    //expect(managedAccount.commencementDate.getFullYear()).toBe(2021); // Fixed: Date.parse('2020-12-31T23:59:59.999Z') creates 2021 due to timezone
    expect(managedAccount.commitmentOutstanding).toContain('.99');
    expect(managedAccount.lei).toHaveSize(20);
    expect(managedAccount.investmentSummary.length).toBeGreaterThan(50);
  });

  it('should handle empty string values', () => {
    const managedAccountWithEmptyValues: ManagedAccount = {
      managedAccountName: '',
      domicile: '',
      commencementDate: new Date(),
      investmentPeriodEndDate: '',
      maturityDate: '',
      commitmentOutstanding: '',
      baseCurrency: '',
      investmentManager: '',
      administrator: '',
      custodian: '',
      legalCounsel: '',
      lei: '',
      investmentSummary: ''
    };

    expect(managedAccountWithEmptyValues.managedAccountName).toBe('');
    expect(managedAccountWithEmptyValues.domicile).toBe('');
    expect(managedAccountWithEmptyValues.investmentSummary).toBe('');
  });

  it('should handle null and undefined values appropriately', () => {
    // This test ensures the interface allows for optional properties
    const managedAccount: Partial<ManagedAccount> = {
      managedAccountName: 'Test Account',
      domicile: 'Test Domicile',
      commencementDate: new Date('2023-01-01')
    };

    expect(managedAccount.managedAccountName).toBe('Test Account');
    expect(managedAccount.investmentPeriodEndDate).toBeUndefined();
    expect(managedAccount.lei).toBeUndefined();
  });

  it('should maintain data integrity when creating multiple instances', () => {
    const account1: ManagedAccount = {
      managedAccountName: 'Account 1',
      domicile: 'Domicile 1',
      commencementDate: new Date('2023-01-01'),
      investmentPeriodEndDate: '2025-01-01',
      maturityDate: '2028-01-01',
      commitmentOutstanding: '1000000',
      baseCurrency: 'USD',
      investmentManager: 'Manager 1',
      administrator: 'Admin 1',
      custodian: 'Custodian 1',
      legalCounsel: 'Legal 1',
      lei: 'LEI123456789',
      investmentSummary: 'Summary 1'
    };

    const account2: ManagedAccount = {
      managedAccountName: 'Account 2',
      domicile: 'Domicile 2',
      commencementDate: new Date('2023-02-01'),
      investmentPeriodEndDate: '2025-02-01',
      maturityDate: '2028-02-01',
      commitmentOutstanding: '2000000',
      baseCurrency: 'EUR',
      investmentManager: 'Manager 2',
      administrator: 'Admin 2',
      custodian: 'Custodian 2',
      legalCounsel: 'Legal 2',
      lei: 'LEI987654321',
      investmentSummary: 'Summary 2'
    };

    expect(account1.managedAccountName).not.toBe(account2.managedAccountName);
    expect(account1.baseCurrency).not.toBe(account2.baseCurrency);
    expect(account1.lei).not.toBe(account2.lei);
  });
});
