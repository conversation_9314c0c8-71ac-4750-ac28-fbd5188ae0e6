<div class="row mr-0 ml-0 filter-panel-header reveal-bi-filter-control">
    <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
        <div class="float-left">
            <nep-tab id="neptab" class="custom-data-analytics-tab" [tabList]=filterTabList
                (OnSelectTab)="onTabClick($event)">
            </nep-tab>
        </div>
    </div>
</div>
<ng-container>
    <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-1 pr-0 pb-4 data-analytics-filter">
        <div class="row mr-0 ml-0  pr-0 pl-0">
            <form name="form" class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0"
                (ngSubmit)="form.valid" #form="ngForm">

                <div class="row ml-0 mr-0" *ngIf="tabName == PortfolioCompany">
                    <!-- Fund Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label  TextTruncate" title="Fund" for="PCFunds">Fund</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect id="pc-fund-multiselect"  #PCFundsMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'"
                                          [kendoDropDownFilter]="filterSettings" 
                                        name="PCFunds" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="portfolioCompanyList.fundList==undefined" [tagMapper]="tagMapper"
                                        [data]="portfolioCompanyList.fundList" [(ngModel)]="portfolioCompanyModel.fundList" [textField]="'fundName'"
                                        (close)="clearSearch(PCFundsMultiSelect)" [valueField]="'fundID'"
                                        (valueChange)="onChangeModel($event,'PCFunds')" [autoClose]="false"
                                        placeholder="Select Fund Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input type="checkbox" id="pc-fund-multiselect-checkbox"class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isPCFundChecked" [indeterminate]="isIndeterminate('PCFunds')" (click)="onSelectedAll('PCFunds')" />
                                                <kendo-label for="chk">{{ toggleAllText('PCFunds') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- Company Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Company Name"
                                    for="PCCompany">Company </div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect id="pc-comp-multiselect"  #PCCompanyMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="PCCompany" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        [ngClass]="{'k-multiselect-search-100':portfolioCompanyModel.companyList.length>0}"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="portfolioCompanyList.companyList==undefined" [tagMapper]="tagMapper"
                                        [data]="portfolioCompanyList.companyList" [(ngModel)]="portfolioCompanyModel.companyList" [textField]="'companyName'"
                                        (close)="clearSearch(PCCompanyMultiSelect)" [valueField]="'companyId'"
                                        (valueChange)="onChangeModel($event,'PCCompany')" [autoClose]="false"
                                        placeholder="Select Company Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="pc-comp-multiselect-checkbox" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isPCCompanyChecked" [indeterminate]="isIndeterminate('PCCompany')" (click)="onSelectedAll('PCCompany')" />
                                                <kendo-label for="chk">{{ toggleAllText('PCCompany') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.companyName">{{ dataItem.companyName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- KPI Type Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="KPI Type"
                                    for="PCModule">KPI Type </div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect id="pc-kpi-type-multiselect"  #PCModuleMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="PCModule" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="portfolioCompanyList.moduleList==undefined" [tagMapper]="tagMapper"
                                        [data]="portfolioCompanyList.moduleList" [(ngModel)]="portfolioCompanyModel.moduleList" [textField]="'aliasName'"
                                        (close)="clearSearch(PCModuleMultiSelect)" [valueField]="'id'"
                                        (valueChange)="onChangeModel($event,'PCModule')" [autoClose]="false"
                                        placeholder="Select KPI Type">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="pc-kpi-type-multiselect-checknox" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isPCModuleChecked" [indeterminate]="isIndeterminate('PCModule')" (click)="onSelectedAll('PCModule')" />
                                                <kendo-label for="chk">{{ toggleAllText('PCModule') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.aliasName">{{ dataItem.aliasName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- KPI Item Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="KPI Line Items"
                                    for="PCKpiItem">KPI Line Items</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselecttree id="pc-kpi-lineitems-multiselect" [clearButton]="false" [fillMode]="'flat'" class="k-dropdown-width-100 k-multiselect-custom k-multiselect-custom-filter k-multiselect-custom-tree k-multiselect-chip-100" [filterable]="true" [checkAll]="portfolioCompanyList?.kpiItems?.length > 0"
                                        [kendoMultiSelectTreeHierarchyBinding]="portfolioCompanyList.kpiItems"
                                        name="PCKpiItem" [(ngModel)]="portfolioCompanyModel.kpiItems"
                                        [ngClass]="(portfolioCompanyList.kpiItems.length == 0 && isKpiItemLoading) && portfolioCompanyModel.moduleList.length > 0 ? 'k-multiselect-custom-tree-loader' : ''"
                                        [disabled]="portfolioCompanyList.kpiItems==undefined || portfolioCompanyModel.moduleList?.length == 0 || portfolioCompanyModel.companyList?.length ==0"
                                        childrenField="items" [expandedKeys]="expandedKpiLineItems"
                                        textField="kpiDisplayName" valueField="id" [tagMapper]="tagMapper"
                                        (valueChange)="onChangeModel($event,'PCKpiItem')" [autoClose]="false"
                                        [checkableSettings]="checkableSettings"
                                        kendoMultiSelectTreeExpandable placeholder="Select KPI Item">
                                        <ng-template kendoMultiSelectTreeGroupTagTemplate let-dataItems *ngIf="portfolioCompanyModel.kpiItems.length>0">
                                            {{ calculateLengthtagMapper(portfolioCompanyModel.kpiItems) }} item(s) selected
                                        </ng-template>
                                        <ng-template kendoMultiSelectTreeNodeTemplate let-dataItem>
                                            <span title="{{ dataItem.kpiDisplayName }}" class="TextTruncate Body-R">{{ dataItem.kpiDisplayName }}</span>
                                        </ng-template>
                                    </kendo-multiselecttree>
                                </span>
                                <div class="loading-input-controls" *ngIf="portfolioCompanyList.kpiItems==undefined">
                                    <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Fx Rates Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label TextTruncate " title="Fx Rates" for="PCFxRates">Fx
                                    Rates</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0 ">
                                <div class="report-field without-border-outline">
                                    <kendo-combobox id="pc-fx-rates" [clearButton]="false" [(ngModel)]="portfolioCompanyModel.fxRates"
                                        [fillMode]="'flat'"  name="PCFxRates"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                        [size]="'medium'" [data]="portfolioCompanyList.fxRates" [filterable]="false"
                                        textField="type" valueField="id" [placeholder]="'Select Currency'"
                                        (valueChange)="onChangeModel($event,'PCFxRates')">
                                    </kendo-combobox>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Currency Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label TextTruncate " title="Currency" for="PCCurrency">
                                    Currency </div>
                            </div>
                            <div
                                class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0  without-border-dropdown">
                                <div class="report-field without-border-outline">
                                    <kendo-combobox id="pc-currency" [clearButton]="false"
                                        [(ngModel)]="portfolioCompanyModel.currenceList" [fillMode]="'flat'"
                                        name="PCCurrency"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                        [size]="'medium'" [data]="portfolioCompanyList.currenceList"
                                        [filterable]="false" textField="currency" valueField="currencyID"
                                        [placeholder]="'Select Currency'"
                                        (valueChange)="onChangeModel($event,'PCCurrency')">
                                    </kendo-combobox>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Period Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Period Range"
                                    for="PCPeriod">Period</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <p-calendar id="pc-period" #myCalendar [styleClass]="'data-analytics-calendar'"
                                        class="tablefilter-dropdrown-width" [readonlyInput]="true"
                                        inputStyleClass="p-custom-calendar date-picker-input" name="PCPeriod"
                                        [(ngModel)]="portfolioCompanyModel.period" dateFormat="yy" [showIcon]="true"
                                        yearRange={{yearRange}} [yearNavigator]="true" view="year"
                                        placeholder="Select Year" selectionMode="range"
                                        (ngModelChange)="onChangeModel($event,'PCPeriod')">
                                    </p-calendar>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0 parent-child-graph-container">
                            <span kendoTooltip [title]="chkPCParentChildGraphDisabled ? parentChildChkDisabled : ''"
                                [ngClass]="{'disabled-chk': chkPCParentChildGraphDisabled}">
                                <input kendoTooltip type="checkbox" id="create-parent-child-graph-checkbox"
                                    class="parent-child-graph k-checkbox k-checkbox-md k-rounded-md chkCopyTo" kendoCheckBox
                                    [checked]="isParentChildGraph" [disabled]="chkPCParentChildGraphDisabled"
                                    (click)="onChangeModel(isParentChildGraph,'PCParentChildGraph')" />
                            </span>
                            <span class="parent-child-graph-title">Create Parent Child Graph</span>
                            <img kendoTooltip [title]="parentChildError" src="assets/dist/images/file-info.svg" alt="info-icon" class="info-icon">
                        </div>
                    </div>
                </div>
                <div class="row ml-0 mr-0" *ngIf="tabName == Deal">
                    <!-- Fund Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Fund"
                                    for="DealFunds">Fund</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect id="deal-fund-multiselect"  #DealFundsMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="DealFunds" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="dealList.fundList==undefined" [tagMapper]="tagMapper"
                                        [data]="dealList.fundList" [(ngModel)]="dealModel.fundList" [textField]="'fundName'"
                                        (close)="clearSearch(DealFundsMultiSelect)" [valueField]="'fundID'"
                                        (valueChange)="onChangeModel($event,'DealFunds')" [autoClose]="false"
                                        placeholder="Select Fund Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="deal-fund-checkbox"type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isDealFundChecked" [indeterminate]="isIndeterminate('DealFunds')" (click)="onSelectedAll('DealFunds')" />
                                                <kendo-label for="chk">{{ toggleAllText('DealFunds') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="dealList.fundList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Company Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Company Name"
                                    for="DealCompany">Company</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect   id="deal-comp-multiselect" #DealCompanyMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="DealCompany" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="dealList.companyList==undefined" [tagMapper]="tagMapper"
                                        [data]="dealList.companyList" [(ngModel)]="dealModel.companyList" [textField]="'companyName'"
                                        (close)="clearSearch(DealCompanyMultiSelect)" [valueField]="'companyId'"
                                        (valueChange)="onChangeModel($event,'DealCompany')" [autoClose]="false"
                                        placeholder="Select Company Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="deal-comp-checkbox" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isDealCompanyChecked"  [indeterminate]="isIndeterminate('DealCompany')" (click)="onSelectedAll('DealCompany')" />
                                                <kendo-label for="chk">{{ toggleAllText('DealCompany') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.companyName">{{ dataItem.companyName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="dealList.companyList==undefined">
                                    <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Data items Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Data items"
                                    for="DealModule">Data items</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect  id="deal-deal-multiselect"  #DealModuleMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="DealModule" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="dealList.moduleList==undefined" [tagMapper]="tagMapper"
                                        [data]="dealList.moduleList" [(ngModel)]="dealModel.moduleList" [textField]="'aliasName'"
                                        (close)="clearSearch(DealModuleMultiSelect)" [valueField]="'id'"
                                        (valueChange)="onChangeModel($event,'DealModule')" [autoClose]="false"
                                        placeholder="Select KPI Type">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="deal-deal-checkbox" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isDealModuleChecked" [indeterminate]="isIndeterminate('DealModule')" (click)="onSelectedAll('DealModule')" />
                                                <kendo-label for="chk">{{ toggleAllText('DealModule') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.aliasName">{{ dataItem.aliasName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="dealList.moduleList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- KPI Item Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Data Line Items"
                                    for="DealKpiItem">Data Line Items</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselecttree id="deal-data-lineitem" [clearButton]="false"  [fillMode]="'flat'" class=" k-dropdown-width-100 k-multiselect-custom k-multiselect-custom-filter  k-multiselect-custom-tree k-multiselect-chip-100" [filterable]="true"  [checkAll]="dealList?.kpiItems?.length > 0"
                                        [kendoMultiSelectTreeHierarchyBinding]="dealList.kpiItems" name="DealKpiItem"
                                        [(ngModel)]="dealModel.kpiItems"
                                        [ngClass]="dealList.kpiItems.length == 0 && dealModel.moduleList.length > 0 ? 'k-multiselect-custom-tree-loader' : ''"
                                        [disabled]="dealList.kpiItems==undefined || dealModel.moduleList?.length == 0 || dealModel.companyList?.length ==0 || dealList.kpiItems.length == 0"
                                        childrenField="items" [expandedKeys]="expandedKpiLineItems"
                                        textField="kpi" valueField="id" [tagMapper]="tagMapper"
                                        (valueChange)="onChangeModel($event,'DealKpiItem')" [autoClose]="false"
                                        kendoMultiSelectTreeExpandable placeholder="Select KPI Item">
                                        <ng-template kendoMultiSelectTreeGroupTagTemplate let-dataItems
                                            *ngIf="dealModel.kpiItems.length>0">
                                            {{ dealModel.kpiItems.length }} item(s) selected
                                        </ng-template>
                                        <ng-template kendoMultiSelectTreeNodeTemplate let-dataItem>
                                            <span title="{{ dataItem.kpi }}" class="TextTruncate Body-R">{{ dataItem.kpi }}</span>
                                        </ng-template>
                                    </kendo-multiselecttree>
                                </span>
                                <div class="loading-input-controls" *ngIf="dealList.kpiItems==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Period Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Period Range"
                                    for="DealPeriod">Period</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <p-calendar #myCalendar [styleClass]="'data-analytics-calendar'" id="deal-calendar"
                                        class="tablefilter-dropdrown-width analytics-period-dropdown"
                                        [readonlyInput]="true" inputStyleClass="p-custom-calendar date-picker-input"
                                        name="PCPeriod" [(ngModel)]="dealModel.period" dateFormat="yy" [showIcon]="true"
                                        yearRange={{yearRange}} [yearNavigator]="true" view="year" inputId="yearpicker"
                                        placeholder="Select Year" selectionMode="range"
                                        (ngModelChange)="onChangeModel($event,'DealPeriod')">
                                    </p-calendar>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row ml-0 mr-0" *ngIf="tabName == Fund">
                    <!-- Fund Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Fund"
                                    for="FundsList">Fund</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect id="fund-multiselect"  #FundsListMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="FundsList" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="fundList.fundList==undefined" [tagMapper]="tagMapper"
                                        [data]="fundList.fundList" [(ngModel)]="fundModel.fundList" [textField]="'fundName'"
                                        (close)="clearSearch(FundsListMultiSelect)" [valueField]="'fundID'"
                                        (valueChange)="onChangeModel($event,'FundsList')" [autoClose]="false"
                                        placeholder="Select Fund Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="fund-checkbox" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isFundFundChecked" [indeterminate]="isIndeterminate('FundsList')" (click)="onSelectedAll('FundsList')" />
                                                <kendo-label for="chk">{{ toggleAllText('FundsList') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="fundList.fundList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Data items Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Data items"
                                    for="fundModel">Data items</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect   id="fund-dataitems-multiselect" #FundModuleMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="FundModule" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="fundList.moduleList==undefined" [tagMapper]="tagMapper"
                                        [data]="fundList.moduleList" [(ngModel)]="fundModel.moduleList" [textField]="'name'"
                                        (close)="clearSearch(FundModuleMultiSelect)" [valueField]="'id'"
                                        (valueChange)="onChangeModel($event,'FundModule')" [autoClose]="false"
                                        placeholder="Select KPI Type">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isFundModuleChecked" [indeterminate]="isIndeterminate('FundModule')" (click)="onSelectedAll('FundModule')"  />
                                                <kendo-label for="chk">{{ toggleAllText('FundModule') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.name">{{ dataItem.name }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="fundList.moduleList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- KPI Item Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Data Line Items"
                                    for="FundKpiItem">Data Line Items</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselecttree  id="fund-datalineitems-multiselect" [clearButton]="false" [fillMode]="'flat'" class=" k-dropdown-width-100 k-multiselect-custom k-multiselect-custom-filter  k-multiselect-custom-tree k-multiselect-chip-100" [filterable]="true"  [checkAll]="fundList?.kpiItems?.length > 0"
                                        [kendoMultiSelectTreeHierarchyBinding]="fundList?.kpiItems" name="FundKpiItem"
                                        [(ngModel)]="fundModel.kpiItems"
                                        [ngClass]="fundList?.kpiItems?.length == 0 && fundModel?.moduleList?.length > 0 ? 'k-multiselect-custom-tree-loader' : ''"
                                        [disabled]="fundList?.kpiItems==undefined || fundModel?.moduleList?.length == 0 || fundList?.kpiItems?.length == 0"
                                        childrenField="items"  [expandedKeys]="expandedKpiLineItems"
                                        textField="kpi" valueField="id" [tagMapper]="tagMapper"
                                        (valueChange)="onChangeModel($event,'FundKpiItem')" [autoClose]="false"
                                        kendoMultiSelectTreeExpandable placeholder="Select KPI Item">
                                        <ng-template kendoMultiSelectTreeGroupTagTemplate let-dataItems
                                            *ngIf="fundModel?.kpiItems?.length>0">
                                            {{ fundModel?.kpiItems?.length }} item(s) selected
                                        </ng-template>
                                        <ng-template kendoMultiSelectTreeNodeTemplate let-dataItem>
                                            <span title="{{ dataItem.kpi }}" class="TextTruncate Body-R">{{ dataItem.kpi }}</span>
                                        </ng-template>
                                    </kendo-multiselecttree>
                                </span>
                                <div class="loading-input-controls" *ngIf="fundList.kpiItems==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Period Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Period Range"
                                    for="FundPeriod">Period</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <p-calendar  id="fund-calendar" #myCalendar [styleClass]="'data-analytics-calendar'"
                                        class="tablefilter-dropdrown-width analytics-period-dropdown"
                                        [readonlyInput]="true" inputStyleClass="p-custom-calendar date-picker-input"
                                        name="PCPeriod" [(ngModel)]="fundModel.period" dateFormat="yy" [showIcon]="true"
                                        yearRange={{yearRange}} [yearNavigator]="true" view="year" inputId="yearpicker"
                                        placeholder="Select Year" selectionMode="range"
                                        (ngModelChange)="onChangeModel($event,'FundPeriod')">
                                    </p-calendar>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row ml-0 mr-0" *ngIf="tabName == Investor">

                    <!-- Investor Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Fund"
                                    for="InvestorList">Investor</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect  id="investor-multiselect" #InvestorListMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="InvestorList" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="investorList.investorList==undefined" [tagMapper]="tagMapper"
                                        [data]="investorList.investorList" [(ngModel)]="investorModel.investorList" [textField]="'investorName'"
                                        (close)="clearSearch(InvestorListMultiSelect)" [valueField]="'investorId'"
                                        (valueChange)="onChangeModel($event,'InvestorList')" [autoClose]="false"
                                        placeholder="Select Investor Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="investor-checkbox"  type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isInvestorListChecked" [indeterminate]="isIndeterminate('InvestorList')"  (click)="onSelectedAll('InvestorList')" />
                                                <kendo-label for="chk">{{ toggleAllText('InvestorList') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.investorName">{{ dataItem.investorName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="investorList.investorList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Fund Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Fund"
                                    for="InvestorFunds">Fund</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect  id="investor-fund-multiselect"  #InvestorFundsMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="InvestorFunds" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="investorList.fundList==undefined" [tagMapper]="tagMapper"
                                        [data]="investorList.fundList" [(ngModel)]="investorModel.fundList" [textField]="'fundName'"
                                        (close)="clearSearch(InvestorFundsMultiSelect)" [valueField]="'fundID'"
                                        (valueChange)="onChangeModel($event,'InvestorFunds')" [autoClose]="false"
                                        placeholder="Select Fund Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="investor-fund-checkbox"  type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isInvestorFundChecked" [indeterminate]="isIndeterminate('InvestorFunds')" (click)="onSelectedAll('InvestorFunds')" />
                                                <kendo-label for="chk">{{ toggleAllText('InvestorFunds') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="investorList.fundList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Company Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Company Name"
                                    for="InvestorCompany">Company</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect id="investor-comp-multiselect"   #InvestorCompanyMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="InvestorCompany" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="investorList.companyList==undefined" [tagMapper]="tagMapper"
                                        [data]="investorList.companyList" [(ngModel)]="investorModel.companyList" [textField]="'companyName'"
                                        (close)="clearSearch(InvestorCompanyMultiSelect)" [valueField]="'companyId'"
                                        (valueChange)="onChangeModel($event,'InvestorCompany')" [autoClose]="false"
                                        placeholder="Select Company Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="investor-comp-checkbox"  type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isInvestorCompanyChecked" [indeterminate]="isIndeterminate('InvestorCompany')" (click)="onSelectedAll('InvestorCompany')" />
                                                <kendo-label for="chk">{{ toggleAllText('InvestorCompany') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.companyName">{{ dataItem.companyName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="investorList.companyList==undefined">
                                    <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Company Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Sections"
                                    for="InvestorSections">Sections</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <kendo-combobox id="investor-sections" [clearButton]="false" [(ngModel)]="investorModel.investorSections"
                                        [fillMode]="'flat'"  name="InvestorSections"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                        [size]="'medium'" [data]="investorList.investorSections" [filterable]="false"
                                        textField="type" valueField="id" [placeholder]="'Select Section'"
                                        (valueChange)="onChangeModel($event,'InvestorSections')">
                                    </kendo-combobox>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Period Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Period Range"
                                    for="InvestorPeriod">Period</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <p-calendar id="investor-calendar"  #myCalendar [styleClass]="'data-analytics-calendar'"
                                        class="tablefilter-dropdrown-width" [readonlyInput]="true"
                                        inputStyleClass="p-custom-calendar date-picker-input" name="PCPeriod"
                                        [(ngModel)]="investorModel.period" dateFormat="yy" [showIcon]="true"
                                        yearRange={{yearRange}} [yearNavigator]="true" view="year" inputId="yearpicker"
                                        placeholder="Select Year" selectionMode="range"
                                        (ngModelChange)="onChangeModel($event,'InvestorPeriod')">
                                    </p-calendar>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row ml-0 mr-0" *ngIf="tabName == ESG">
                    <!-- Fund Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label  TextTruncate" title="Fund" for="ESGFunds">Fund</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect  id="esg-multiselect"  #ESGFundsMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="ESGFunds" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="ESGList.fundList==undefined" [tagMapper]="tagMapper"
                                        [data]="ESGList.fundList" [(ngModel)]="ESGModel.fundList" [textField]="'fundName'"
                                        (close)="clearSearch(ESGFundsMultiSelect)" [valueField]="'fundID'"
                                        (valueChange)="onChangeModel($event,'ESGFunds')" [autoClose]="false"
                                        placeholder="Select Fund Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input id="esg-checkbox" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isESGFundChecked" [indeterminate]="isIndeterminate('ESGFunds')" (click)="onSelectedAll('ESGFunds')" />
                                                <kendo-label for="chk">{{ toggleAllText('ESGFunds') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.fundName">{{ dataItem.fundName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="ESGList.fundList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Company Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M mandatory-label filter-label TextTruncate " title="Company Name"
                                    for="ESGCompany">Company</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect  id="esg-comp-multiselect"  #ESGCompanyMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                        name="ESGCompany" [virtual]="virtualMultiSelect" [clearButton]="false"
                                        class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="ESGList.companyList==undefined" [tagMapper]="tagMapper"
                                        [data]="ESGList.companyList" [(ngModel)]="ESGModel.companyList" [textField]="'companyName'"
                                        (close)="clearSearch(ESGCompanyMultiSelect)" [valueField]="'companyId'"
                                        (valueChange)="onChangeModel($event,'ESGCompany')" [autoClose]="false"
                                        placeholder="Select Company Name">
                                        <ng-template kendoMultiSelectHeaderTemplate>
                                            <div class="inline-container">
                                                <input  id="esg-comp-checkbox" type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isESGCompanyChecked" [indeterminate]="isIndeterminate('ESGCompany')"  (click)="onSelectedAll('ESGCompany')" />
                                                <kendo-label for="chk">{{ toggleAllText('ESGCompany') }}</kendo-label>
                                            </div>
                                        </ng-template>
                                        <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                            <span class="TextTruncate pl-1 Body-R" [title]="dataItem.companyName">{{ dataItem.companyName }}</span>
                                        </ng-template>
                                    </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="ESGList.companyList==undefined">
                                    <i aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- KPI Type Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="KPI Type"
                                    for="ESGModule">KPI Type</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field">
                                    <kendo-multiselect  id="esg-kpi-multiselect"  #ESGModuleMultiSelect [checkboxes]="true" [rounded]="'medium'" [fillMode]="'solid'" [kendoDropDownFilter]="filterSettings"
                                    name="ESGModule" [virtual]="virtualMultiSelect" [clearButton]="false"
                                    class="k-multiselect-custom k-multiselect-custom-filter k-dropdown-width-100 multiselect-revealbi-custom" [disabled]="ESGList.moduleList==undefined" [tagMapper]="tagMapper"
                                    [data]="ESGList.moduleList" [(ngModel)]="ESGModel.moduleList" [textField]="'name'"
                                    (close)="clearSearch(ESGModuleMultiSelect)" [valueField]="'id'"
                                    (valueChange)="onChangeModel($event,'ESGModule')" [autoClose]="false"
                                    placeholder="Select KPI Type">
                                    <ng-template kendoMultiSelectHeaderTemplate>
                                        <div class="inline-container">
                                            <input id="esg-kpi-checkbox"  type="checkbox" class="k-checkbox k-checkbox-md k-rounded-md chkCopyTo"  kendoCheckBox [checked]="isESGModuleChecked" [indeterminate]="isIndeterminate('ESGModule')" (click)="onSelectedAll('ESGModule')" />
                                            <kendo-label for="chk">{{ toggleAllText('ESGModule') }}</kendo-label>
                                        </div>
                                    </ng-template>
                                    <ng-template kendoMultiSelectItemTemplate let-dataItem>
                                        <span class="TextTruncate pl-1 Body-R" [title]="dataItem.name">{{ dataItem.name }}</span>
                                    </ng-template>
                                </kendo-multiselect>
                                </span>
                                <div class="loading-input-controls" *ngIf="ESGList.moduleList==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- KPI Item Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="KPI Type"
                                    for="ESGKpiItem">KPI Line Items</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <span class="report-field k-multiselect-tree-custom">
                                    <kendo-multiselecttree id="esg-kpi-lineitems"  [clearButton]="false"   [fillMode]="'flat'" class=" k-dropdown-width-100 k-multiselect-custom k-multiselect-custom-filter  k-multiselect-custom-tree k-multiselect-chip-100" [filterable]="true"  [checkAll]="ESGList.kpiItems?.length > 0"
                                        [kendoMultiSelectTreeHierarchyBinding]="ESGList.kpiItems" name="ESGKpiItem"
                                        [(ngModel)]="ESGModel.kpiItems"
                                        [ngClass]="(ESGList?.kpiItems.length == 0 && isEsgKpiItemLoading) && ESGModel?.moduleList?.length > 0 ? 'k-multiselect-custom-tree-loader' : ''"
                                        [disabled]="ESGList?.kpiItems==undefined || ESGModel?.moduleList?.length == 0 || ESGModel?.companyList?.length ==0"
                                        childrenField="items"  [expandedKeys]="expandedKpiLineItems"
                                        textField="kpiDisplayName" valueField="id" [tagMapper]="tagMapper"
                                        [checkableSettings]="checkableSettings"
                                        (valueChange)="onChangeModel($event,'ESGKpiItem')" [autoClose]="false"
                                        kendoMultiSelectTreeExpandable placeholder="Select KPI Item">
                                        <ng-template kendoMultiSelectTreeGroupTagTemplate let-dataItems
                                            *ngIf="ESGModel.kpiItems.length>0">
                                            {{ calculateLengthtagMapper(ESGModel.kpiItems) }} item(s) selected
                                        </ng-template>
                                        <ng-template kendoMultiSelectTreeNodeTemplate let-dataItem>
                                            <span title="{{ dataItem.kpiDisplayName }}" class="TextTruncate Body-R">{{ dataItem.kpiDisplayName }}</span>
                                        </ng-template>
                                    </kendo-multiselecttree>
                                </span>
                                <div class="loading-input-controls" *ngIf="ESGList.kpiItems==undefined"><i
                                        aria-hidden="true" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Fx Rates Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label TextTruncate " title="Fx Rates" for="ESGFxRates">Fx
                                    Rates</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <kendo-combobox id="esg-fxrates"  [clearButton]="false" [(ngModel)]="ESGModel.fxRates"
                                        [fillMode]="'flat'" name="ESGFxRates"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                        [size]="'medium'" [data]="portfolioCompanyList.fxRates" [filterable]="false"
                                        textField="type" valueField="id" [placeholder]="'Select Currency'"
                                        (valueChange)="onChangeModel($event,'ESGFxRates')">
                                    </kendo-combobox>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Currency Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label TextTruncate " title="Currency" for="ESGCurrency">
                                    Currency</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <kendo-combobox id="esg-currency" [clearButton]="false" [(ngModel)]="ESGModel.currenceList"
                                        [fillMode]="'flat'"  name="ESGCurrency"
                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-32"
                                        [size]="'medium'" [data]="portfolioCompanyList.currenceList"
                                        [filterable]="false" textField="currency" valueField="currencyID"
                                        [placeholder]="'Select Currency'"
                                        (valueChange)="onChangeModel($event,'ESGCurrency')">
                                    </kendo-combobox>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Period Control -->
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">

                        <div class="row mr-0 ml-0">
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pl-0 pr-0 ">
                                <div class="Caption-M filter-label mandatory-label TextTruncate " title="Period Range"
                                    for="ESGPeriod">Period</div>
                            </div>
                            <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0">
                                <div class="report-field">
                                    <p-calendar id="esg-calendar" #myCalendar [styleClass]="'data-analytics-calendar'"
                                        class="tablefilter-dropdrown-width" [readonlyInput]="true"
                                        inputStyleClass="p-custom-calendar date-picker-input" name="ESGPeriod"
                                        [(ngModel)]="ESGModel.period" dateFormat="yy" [showIcon]="true"
                                        yearRange={{yearRange}} [yearNavigator]="true" view="year"
                                        placeholder="Select Year" selectionMode="range"
                                        (ngModelChange)="onChangeModel($event,'ESGPeriod')">
                                    </p-calendar>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 pl-3  pr-3  filter-content-padding">
                        <div class="col-md-12 col-lg-12 col-xl-12 col-sm-12 col-12 col-xs-12 pr-0 pl-0 parent-child-graph-container">
                            <span kendoTooltip [title]="chkEsgParentChildGraphDisabled ? parentChildChkDisabled : ''"
                                [ngClass]="{'disabled-chk': chkEsgParentChildGraphDisabled}">
                                <input type="checkbox" id="create-parent-child-graph-checkbox-esg"
                                    class="parent-child-graph k-checkbox k-checkbox-md k-rounded-md chkCopyTo" kendoCheckBox
                                    [checked]="isEsgParentChildGraph" [disabled]="chkEsgParentChildGraphDisabled"
                                    (click)="onChangeModel(isEsgParentChildGraph,'EsgParentChildGraph')" />
                            </span>
                            <span class="parent-child-graph-title">Create Parent Child Graph</span>
                            <img kendoTooltip [title]="parentChildError" src="assets/dist/images/file-info.svg" alt="info-icon" class="info-icon">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <p class="pc-warning-text"*ngIf="tabName === 'ESG' || tabName === 'PortfolioCompany'">{{parentChildWarningNote}}</p>
</ng-container>
<div>
    <confirm-modal *ngIf="confirmDelete" primaryButtonName="Ok" secondaryButtonName="Cancel"
        (primaryButtonEvent)="Delete()" modalTitle="Delete" (secondaryButtonEvent)="CancelDelete()">
        <div>Are you sure you want to delete the saved filter?</div>
    </confirm-modal>
</div>