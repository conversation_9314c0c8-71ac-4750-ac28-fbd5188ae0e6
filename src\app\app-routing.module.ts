import { UnauthorizedaccesComponent } from './components/unauthorizedacces/unauthorizedacces.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { environment } from 'src/environments/environment';
import { BarChartComponent } from './components/chart/barChart';
import { BubbleChartComponent } from './components/chart/bubbleChart';
import { DonutChartComponent } from './components/chart/donutChart';
import { LineBarChartComponent } from './components/chart/lineBarChart';
import { LineChartComponent } from './components/chart/lineChart';
import { MultilineChartComponent } from './components/chart/multilineChart';
import { AuthGuard } from './guards';
import { OpenDocumentComponent } from './components/repository/open-document/open-document.component';
import { ViewPCAduitlogsComponent } from './components/portfolioCompany/view-pc-aduitlogs/view-pc-aduitlogs.component';
import { MasterComponent } from './components/master/master.component';
import { MasterModule } from './components/master/master.module';
import { PageNotFoundComponent } from './components/page-not-found/page-not-found.component';
import { ViewEsgAduitlogsComponent } from './components/esg/view-esg-auditlogs/view-esg-aduitlogs.component';
import { StackedPercentageChartComponent } from './components/data-analytics/custom-visualizations/stacked-percentage-chart.component';
import { WaterfallChartComponent } from './components/data-analytics/custom-visualizations/waterfall-chart.component';
import {EmailConfigurationPageComponent} from './components/repository-configuration/email-configuration-page/email-configuration-page.component';
import { get } from 'http';

const routes: Routes = [
  {
    path: '',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Dashboard',
        url: ''
      }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/home/<USER>').then(mod => mod.HomeModule),
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'home',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Dashboard',
        url: ''
      }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/home/<USER>').then(mod => mod.HomeModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'dashboard-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Dashboard',
        url: ''
      },
      {
        label: 'Dashboard Configuration',
        url: '/dashboard-configuration',
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/dashboard-tracker/dashboard-configuration/dashboard-configuration.module').then(mod => mod.DashboardConfigurationModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'investment-company',
    component: MasterComponent,
    data: {
      isFromService:true,
      breadcrumb: [
      {
        label: 'Investment Company',
        url: '/investment-company'
      }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/investment-companymodel.module').then(mod => mod.InvestmentCompanyModule)
      },
    ],
    canActivate: [AuthGuard],
  },
    {
    path: 'managed-accounts',
    component: MasterComponent,
    data: {
      isFromService:true,
      breadcrumb: [
      {
        label: 'Managed Accounts',
        url: '/managed-accounts'
      }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/managed-accounts/managed-accounts.module').then(mod => mod.ManagedAccountsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'clo-list',
    component: MasterComponent,
    data: {
      isFromService:true,
      breadcrumb: [
      {
        label: 'Collateral Loan Obligation(CLO)',
        url: '/clo-list'
      }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/clo-list/clo-list.module').then(mod => mod.CloListModule)

      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-clo',
    component: MasterComponent,
    data: {
      isFromService:true,
      breadcrumb: [
      {
        label: 'Collateral Loan Obligation(CLO)',
        url: '/clo-list'
      },
    {
      label: 'Add CLO',
      url:''
    }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/add-clo-list.module').then(mod => mod.AddCloComponentModule)

      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'upload-performance-data',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/flat-table/upload-performance-data.module').then(mod => mod.UploadPerformanceDataModule)

      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'view-clo-summary/:id',
    component: MasterComponent,
    data:{
      isFromService:true,
      breadcrumb:[
        {
          label: 'Collateral Loan Obligation(CLO)',
          url: '/clo-list',
        },
        {
          label: 'Update CLO',
          url: '',
          isIdRoute:true
        }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/view-clo-summary.module').then(mod => mod.ViewCloSummaryModule)

      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'clo-kpi-history',
    component: MasterComponent,
    data:{
      isFromService:true,
    }  ,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/clo-kpi-history.module').then(mod => mod.CloKpiHistoryModule)

      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'clo-page-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'CLO Page Configuration',
        url: ''
      }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/page-configuration.module').then(mod => mod.PageConfigurationModule)

      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'create-fund/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Funds',
        url: '/fund-list'
      },
      {
        label: 'Add Fund',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/funds/add-funds.module').then(mod => mod.AddFundModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'create-fund',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Funds',
        url: '/fund-list'
      },
      {
        label: 'Add Fund',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/funds/add-funds.module').then(mod => mod.AddFundModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'fund-list',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Funds',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: "master",
        path: "",
        loadChildren: () => import('./components/funds/fund-list.module').then(mod => mod.FundListModule),
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'repository',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Repository',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/repository/repository-list.module').then(mod => mod.RepositoryListModule),
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'open-document/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Open Document',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        component: OpenDocumentComponent,
      },
    ]
  },
  {
    path: 'open-document',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Open Document',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        component: OpenDocumentComponent,
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-firm/:id',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Firms',
        url: '/firm'
      },
      {
        label: 'Update Firm',
        url: '',
        isIdRoute:true
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/firm/add-firm.module').then(mod => mod.AddFirmModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'firm',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Firms',
        url: ''
      }
    ]},
    children: [
      {
        outlet: "master",
        path: "",
        loadChildren: () => import('./components/firm/firm-list.module').then(mod => mod.FirmListModule),
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-firm',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Firms',
        url: '/firm'
      },
      {
        label: 'Add Firm',
        url: '',
        isIdRoute:false
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/firm/add-firm.module').then(mod => mod.AddFirmModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path:  'firm-details/:id',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Firms',
        url: '/firm'
      },
      {
        label: 'Firm Details',
        url: '',
        isIdRoute:true
      }
    ]},
    children: [
      {
        outlet: "master",
        path: "",
        loadChildren: () => import('./components/firm/firm-details.module').then(mod => mod.FirmDetailsModule),
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'fund-details/:id',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Funds',
        url: '/fund-list'
      },
      {
        label: 'Add Fund',
        url: '',
        isIdRoute:true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/funds/fund-details.module').then(mod => mod.FundDetailsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'portfolio-company',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Portfolio Company',
        url: ''
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/portfolioCompany/portfolioCompany-list.module').then(mod => mod.PortfolioCompanyListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'pc-list-dashboard',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Portfolio Company',
        url: ''
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/portfolioCompany/portfolioCompany-new-list.module').then(mod => mod.PortfolioCompanyNewListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'portfolio-company-detail/:id',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Portfolio Company',
        url: '/portfolio-company'
      },
      {
        label: 'Company Details',
        url: '',
        isIdRoute:true
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/portfolioCompany/portfolio-details.module').then(mod => mod.PortfolioDetailsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-portfolio-company/:id',
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Portfolio Company',
        url: '/portfolio-company'
      },
      {
        label: 'Update Company',
        url: '',
        isIdRoute:true
      }
    ]},
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/portfolioCompany/add-portfolioCompany.module').then(mod => mod.AddPCModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-portfolio-company',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Portfolio Company',
        url: '/portfolio-company'
      },
      {
        label: 'Add Company',
        url: ''
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/portfolioCompany/add-portfolioCompany.module').then(mod => mod.AddPCModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'save-deal',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Deals',
        url: '/deal-list'
      },
      {
        label: 'Add Deals',
        url: '',
        isIdRoute:false
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/Deal/save-deals.module').then(mod => mod.SaveDealsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'save-deal/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Deals',
        url: '/deal-list'
      },
      {
        label: 'Update Deals',
        url: '',
        isIdRoute:true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/Deal/save-deals.module').then(mod => mod.SaveDealsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'deal-list',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Deals',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/Deal/deal-list.module').then(mod => mod.DealListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'deal-details/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Deals',
        url: '/deal-list'
      },
      {
        label: 'Deal Details',
        url: '',
        isIdRoute:true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/Deal/deal-details.module').then(mod => mod.DealDetailsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'pipeline/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Pipeline',
        url: '/pipeline-list'
      },
      {
        label: 'Pipeline',
        url: '',
        isIdRoute: true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/pipeline/add-pipeline.module').then(mod => mod.AddPipelineModule),
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'pipeline',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Pipeline',
        url: '/pipeline-list'
      },
      {
        label: 'Add Pipeline',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/pipeline/add-pipeline.module').then(mod => mod.AddPipelineModule),
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'pipeline-list',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Pipeline',
        url: '/pipeline-list'
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/pipeline/pipeline-list.module').then(mod => mod.PipelineListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'pipeline-details/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Pipeline',
        url: '/pipeline-list'
      },
      {
        label: 'Pipeline',
        url: '',
        isIdRoute: true
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/pipeline/pipeline-details.module').then(mod => mod.PipelineDetailModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'bar-chart',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        component: BarChartComponent,
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'bubble-chart',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        component: BubbleChartComponent,
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'donut-chart',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        component: DonutChartComponent,
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'line-chart',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        component: LineChartComponent,
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'multiline-chart',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        component: MultilineChartComponent,
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'line-bar-chart',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        component: LineBarChartComponent,
      },
    ],
    canActivate: [AuthGuard],
  },
{
    path: 'reports/Attribution',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Reports',
        url: ''
      },
      {
        label: 'Attribution Report',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/reports/attribution/attribution-reports.module').then(mod => mod.AttributionReportsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'reports/Holdings',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Reports',
        url: ''
      },
      {
        label: 'Top Holdings',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/reports/holdings/holding-reports.module').then(mod => mod.HoldingReportsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'bulk-upload',
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Bulk Upload',
        url: ''
      }
    ]
    },
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/file-uploads/bulk-upload.module').then(mod => mod.BulkUploadModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'cashflow-list',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Cashflow',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/cashflow/cashflow-list.module').then(mod => mod.CashflowListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'cashflow/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Cashflow',
        url: '/cashflow-list'
      },
      {
        label: 'Cashflow',
        url: '',
        isIdRoute:true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/cashflow/cashflow.module').then(mod => mod.CashflowModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'cashflow',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Cashflow',
        url: '/cashflow-list'
      },
      {
        label: 'Cashflow',
        url: '',
        isIdRoute:false
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/cashflow/cashflow.module').then(mod => mod.CashflowModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'change-password/:id',
    loadChildren: () => import('./components/auth/login.module')
      .then(mod => mod.LoginModule),
    canActivate: [AuthGuard],
  },
  {
    path: 'audit-logs',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Portfolio Company',
        url: '/portfolio-company'
      },
      {
        label: 'Audit Logs',
        url: '',
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        component: ViewPCAduitlogsComponent,
        loadChildren: () => import('./components/portfolioCompany/view-pc-aduitlogs/view-pc-auditlogs.module').then(mod => mod.ViewPcAuditLogModule)

      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'esg-audit-logs',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'ESG',
        url: ''
      },
      {
        label: 'ESG Details',
        url: '/ESG'
      },
      {
        label: 'Audit Logs',
        url: '',
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        component: ViewEsgAduitlogsComponent,
        loadChildren: () => import('./components/esg/view-esg-auditlogs/view-esg-auditlogs.module').then(mod => mod.ViewEsgAuditLogModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'kpi-list',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Kpi List & Mapping',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/kpi/kpi-list.module').then(mod => mod.KpiListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: "401",
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'UnAuthorized',
        url: ''
      },
    ]
    },
    children: [{
      outlet: 'master',
      path: '',
      component: UnauthorizedaccesComponent
    // loadChildren: () => import('./components/unauthorizedacces/unauthorizedaccess.module').then(mod => mod.UnAuthorizedModule)
    }
    ]
  },
  {
    path: "404",
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Not Found',
        url: ''
      },
    ]
    },
    children: [{
      outlet: 'master',
      path: '',
      component: PageNotFoundComponent
    }
    ]
  },
  {
    path: 'login',
    loadChildren: () => import('./components/auth/login.module').then(mod => mod.LoginModule)
  },
  {
    path: "in",
    loadChildren: ()=> import('./components/Authentication/oauth-login.module').then(mod => mod.OAuthLoginModule),
  },
  {
    path: "out",
    loadChildren: ()=> import('./components/Authentication/oauth-logout.module').then(mod => mod.OAuthLogoutModule),
  },
  {
    path: "refresh",
    loadChildren: ()=> import('./components/Authentication/oauth-refresh.module').then(mod => mod.OAuthRefreshModule),
  },
  {
    path: 'reset-password/:id',
    loadChildren: () => import('./components/auth/login.module').then(mod => mod.LoginModule)
  },
  {
    path: 'report-template',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Report Template',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: "master",
        path: "",
        loadChildren: () => import('./components/report-page-configuration/report-page-configuration.module').then(mod => mod.PageConfigurationModule)
      }
    ]
  },
  {
    path: 'fxrates',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Fx Rates',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/fxrates/fxrates.module').then(mod => mod.FxratesModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'growth-report',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Report Templates',
        url: '/report-template'
      },
      {
        label: 'Growth Report Configuration',
        url: '',
        isIdRoute: false
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/growth-report/growth-report.module').then(mod => mod.GrowthReportModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'lp-report-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Report Templates',
        url: '/report-template'
      },
      {
        label: 'Lp Report Configuration',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/lp-report-config-list/lp-report-config-list.module').then(mod => mod.LpReportConfigListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'repository-configuration',
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Data Collection',
        url: ''
      }
    ]
    },
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/repository-configuration/repository-configuration.module').then(mod => mod.RepositoryConfigurationModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'lp-report-template/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Report Templates',
        url: '/report-template'
      },
      {
        label: 'Report Templates',
        url: '/lp-report-configuration'
      },
      {
        label: 'Add Lp Report Template',
        url: '',
        isIdRoute:true
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/lp-report-config/lp-report-config.module').then(mod => mod.LpReportConfigModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'lp-report-template',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Report Templates',
        url: '/report-template'
      },
      {
        label: 'Report Templates',
        url: '/lp-report-configuration'
      },
      {
        label: 'Add Lp Report Template',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/lp-report-config/lp-report-config.module').then(mod => mod.LpReportConfigModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'workflow/company-draft/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Company',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: "master",
        path: "",
        loadChildren: () => import('./components/workflow/portfolio-company-draft/portfolio-company-draft.module').then(mod => mod.PortfolioCompanyDraftModule)
      }
    ],
    canActivate: [AuthGuard]
  },
  {
    path: 'workflow/group-access',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'User Access Management',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: "master",
        path: "",
        loadChildren: () => import('./components/workflow/workflow-group-access/workflow-group-access.module').then(mod => mod.WorkflowGroupAccessModule)
      }
    ]
  },
  {
    path: 'fund-report-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Report Templates',
        url: '/report-template'
      },
      {
        label: 'Fund Report Configuration',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/fund-report/fund-report.module').then(mod => mod.FundreportModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'page-config',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Page Configuration',
        url: '',
        isIdRoute: false
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/page-settings/page-settings.module').then(mod => mod.PageSettingsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'addinvestor',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Investors',
        url: '/investorlist'
      },
      {
        label: 'Add Investor',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/investor/addinvestor.module').then(mod => mod.AddinvestorModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  ,
  {
    path: 'add-investment-company/:id',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/add-investment-company-model.module').then(mod => mod.AddInvestmentCompanyModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-managed-account',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Managed Accounts',
        url: '/managed-accounts'
      },
      {
        label: 'Add Managed Account',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/managed-accounts/add-managed-account-form/add-managed-account.module').then(mod => mod.AddManagedAccountModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-managed-account/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Managed Accounts',
        url: '/managed-accounts'
      },
      {
        label: 'Update Managed Account',
        url: '',
        isIdRoute: true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/managed-accounts/add-managed-account-form/add-managed-account.module').then(mod => mod.AddManagedAccountModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'view-company-details/:id',
    component: MasterComponent,
    data:{
      isFromService:true,
      breadcrumb:[
        {
          label: 'Investment Company',
          url: '/investment-company',
        },
        {
          label: 'Update Investment Company',
          url: '',
          isIdRoute:true
        }
      ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/view-company-details.module').then(mod => mod.ViewCompanyDeatilsComponentModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'view-managed-account-details/:id',
    component: MasterComponent,
    data:{
      isFromService:true,
      breadcrumb:[
        {
          label: 'Managed Accounts',
          url: '/managed-accounts',
        },
        {
          label: 'View Managed Account',
          url: '',
          isIdRoute:true
        }
      ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/managed-accounts/view-managed-account-details/view-managed-account-details.module').then(mod => mod.ViewManagedAccountDetailsComponentModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'addinvestor/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Investors',
        url: '/investorlist'
      },
      {
        label: 'Update Investor',
        url: '',
        isIdRoute:true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/investor/addinvestor.module').then(mod => mod.AddinvestorModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'investorlist',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Investors',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/investor/investorlist/investorlist.module').then(mod => mod.InvestorListModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'investor-details/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Investment',
        url: ''
      },
      {
        label: 'Investors',
        url: '/investorlist'
      },
      {
        label: 'Investor Details',
        url: '',
        isIdRoute:true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/investor/investorDetails/investorDetails.module').then(mod => mod.InvestorDetailsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'client-reporting',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Reports',
        url: ''
      },
      {
        label: 'Client Reporting',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/client-reporting/client-reporting.module').then(mod => mod.ClientReportingModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'internal-report-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Report Templates',
        url: '/report-template'
      },
      {
        label: 'Internal Report Configuration',
        url: '',
        isIdRoute: false
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/internal-report/internal-report.module').then(mod => mod.InternalReportModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'consolidated-report-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Report Templates',
        url: '/report-template'
      },
      {
        label: 'Consolidated Report Configuration',
        url: '',
        isIdRoute: false
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/consolidated-report/consolidated-report.module').then(mod => mod.ConsolidatedReportModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'report-download',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Reports',
        url: ''
      },
      {
        label: 'Report Download',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/report-download/report-download.module').then(mod => mod.ReportDownloadModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'valuation-model',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Valuation Model',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/Valuation-Model/valuation-model.module').then(mod => mod.ValuationModelModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'ESG',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'ESG',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/esg/esg-model.module').then(mod => mod.EsgModel)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'report-download-bg/:id',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/report-download/report-download-bg/report-download-bg.module').then(mod => mod.ReportDownloadBgModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'export-bg/:id',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/report-download/report-download-bg/report-download-bg.module').then(mod => mod.ReportDownloadBgModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'fileuploadstatus',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/master/file-upload-error/file-upload-error.module').then(mod => mod.FileUploadErrorModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'request-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Data Collection',
        url: ''
      },
      {
        label: 'Request Configuration',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/data-collection/request-config/request-config.module').then(mod => mod.RequestConfigModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'request-configuration-details/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Data Collection',
        url: ''
      },
      {
        label: 'Request Configuration',
        url: '/request-configuration'
      },
      {
        label: 'Add or Edit Request',
        url: '',
        isIdRoute:true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/data-collection/request-config/request-details/request-details.module').then(mod => mod.RequestDetailsModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'clo-kpi-configuration',
    component: MasterComponent,
    data:{
      breadcrumb:[{
        label: 'Admin',
          url: '',
      },
      {
        label: 'CLO KPI Configuration',
          url: '/clo-kpi-configuration',
      }]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/clo/kpi-configuration/kpi-configuration.module').then(mod => mod.KpiConfigurationModule)

      },
    ],
    canActivate: [AuthGuard],
  },

  {
    path: 'data-request',
    component: MasterComponent,
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/data-collection/data-request/data-request.module').then(mod => mod.DataRequestModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'data-ingestion',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Data Ingestion',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/data-extraction/data-ingestion/data-ingestion.module').then(mod => mod.DataIngestionModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'fetch-file/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Data Ingestion',
        url: '/data-ingestion'
      },
      {
        label: 'Fetch Files',
        url: ''
      },
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/data-extraction/data-ingestion-fetchfile/data-ingestion-fetchfile.module').then(mod => mod.DataIngestionFetchFileModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'extraction',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Data Ingestion',
        url: '/data-ingestion'
      },
      {
        label: 'Extraction',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/data-extraction/extraction/data-extraction.module').then(mod => mod.DataExtractionModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  ,
  {
    path: 'email-configuration',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Email Notification',
        url: '/repository-configuration',
        queryParams: { activeTab: 'Email Notification' }
      },
      {
        label: 'User Configuration',
        url: ''
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
       loadChildren: () => import('./components/repository-configuration/email-configuration-page/email-configuration-page.module').then(mod => mod.EmailConfigurationPageModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'edit-email-group/:id',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Email Notification',
        url: '/repository-configuration'
      },
      {
        label: 'User Configuration',
        url: ''
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/repository-configuration/email-configuration-page/add-email-group/add-email-group.module').then(mod => mod.AddEmailGroupModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'add-email-group',
    component: MasterComponent,
    data:  {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Email Notification',
        url: '/repository-configuration'
      },
      {
        label: 'User Configuration',
        url: ''
      }
    ]},
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/repository-configuration/email-configuration-page/add-email-group/add-email-group.module').then(mod => mod.AddEmailGroupModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'email-reminder/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Email Notification',
        url: '/repository-configuration'
      },
      {
        label: 'Email Reminder Settings',
        url: '',
        isIdRoute: true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/repository-configuration/email-remainder-page/email-remainder-page.module').then(mod => mod.EmailRemainderPageModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  {
    path: 'email-reminder-edit/:id',
    component: MasterComponent,
    data: {
      breadcrumb: [{
        label: 'Admin',
        url: ''
      },
      {
        label: 'Email Notification',
        url: '/repository-configuration'
      },
      {
        label: 'Email Reminder Settings',
        url: '',
        isIdRoute: true
      }
    ]
    },
    children: [
      {
        outlet: 'master',
        path: '',
        loadChildren: () => import('./components/repository-configuration/email-remainder-page/email-remainder-page.module').then(mod => mod.EmailRemainderPageModule)
      },
    ],
    canActivate: [AuthGuard],
  },
  { path: "stacked-percentage-chart",  pathMatch: 'full',component: StackedPercentageChartComponent },
  { path: "waterfall-chart",  pathMatch: 'full',component: WaterfallChartComponent },
  { path: '**', pathMatch: 'full',component: PageNotFoundComponent }
];
@NgModule({
  imports: [MasterModule, RouterModule.forRoot(routes, { useHash: true },)],
  exports: [RouterModule],
  providers: [
    { provide: 'BASE_URL', useFactory: getBaseUrl },
    { provide: 'apiGatewayBaseUrl', useFactory: getApiGatewayBaseUrl },
    { provide: 'apiIngestionBaseUrl', useFactory: getApiIngestionBaseUrl },
    { provide: 'Pager_Option', useFactory: getPagerOption },
  ],
})
export class AppRoutingModule { }

export function getBaseUrl() {
  return environment.apiBaseUrl;
}

export function getApiGatewayBaseUrl() {
  return environment.apiGatewayBaseUrl;
}
export function getApiIngestionBaseUrl() {
  return environment.ingestion_api_url;
}

export function getPagerOption() {
  return '[10,25,100]';
}